 [TOKEN MANAGER] Token Manager inizializzato
 [DRAG FIX] Removed setup-animation element from DOM
 [SOCKET INIT] Creating socket with token: Present
 [SOCKET HANDLERS] Handler socket aggiuntivi inizializzati
 [LOCAL GAME] Manager caricato e pronto
 [ONLINE GAME] Manager caricato e pronto
 [GAME MODE] Manager principale caricato e pronto
 [NAMES PROTECTION] Sistema di protezione nomi caricato
 [SMOOTH LOADING] Inizializzazione sistema loading fluido
 [SMOOTH LOADING] Sistema di loading fluido caricato
 [PRELOAD] Avvio precaricamento immagini delle carte: Array(4)
 [PLAYER NAMES] Partita online rilevata - NON imposto player1Name con username loggato
 [INIT] Pagina di gioco rilevata, evito fullResetGameUI per prevenire refresh continui
 [PSN] Inizializzazione sistema PSN...
 [MULTIPLAYER] DOMContentLoaded - Inizio inizializzazione
 [MULTIPLAYER] URL: http://localhost:3000/game
 [MULTIPLAYER] Mantengo parametri URL per evitare redirect durante inizializzazione gioco
 [MULTIPLAYER] Dati di gioco trovati in sessionStorage: {"gameId":"I9KTOQ","color":"black","playerId":"SXcYhcjDerYA2bVjAAAX","opponentId":"gbyArfSOUKYehImeAAAd","opponent":{"id":1,"name":"bruscolino","rating":1000}}
 [MULTIPLAYER] URL corrente: http://localhost:3000/game
 [MULTIPLAYER] Parametri URL: 
 [MULTIPLAYER] Dati del match salvati, in attesa della connessione socket
 [MULTIPLAYER] Flag processingGameData impostato a true
 [MULTIPLAYER] Socket pronto, processando dati del match pendenti
 [HAND SLOTS] Creazione slot per le carte nelle aree delle mani
 [HAND SLOTS] Creati slot per la mano del giocatore 1
 [HAND SLOTS] Creati slot per la mano del giocatore 2
 [ANIMATION] Osservatore impostato per rilevare il completamento dell'animazione
 [ONLINE ENHANCER] DOM pronto, inizializzo enhancer...
 victory-fix.js caricato.
 [PRELOAD] SUCCESSO precaricamento 1/4: http://localhost:3000/img/carte/card-back.webp
 [PRELOAD] SUCCESSO precaricamento 4/4: http://localhost:3000/img/Cover carte/cover.png
 [SOCKET CONNECT] myPlayerId attuale: JatI2Twaf4mwsY0ZAAAf socket.id: JatI2Twaf4mwsY0ZAAAf
 [PRELOAD] SUCCESSO precaricamento 2/4: http://localhost:3000/img/carte/card-back.png
 [PRELOAD] SUCCESSO precaricamento 3/4: http://localhost:3000/img/Cover carte/cover.webp
 [MATCH FOUND] Reset flags protezione turno per nuova partita
 [MATCH FOUND] ===== INIZIO HANDLE MATCH FOUND =====
 [MATCH FOUND] Ricevuto evento matchFound: Object
 [MATCH FOUND] Stack trace: Error
    at handleMatchFound (http://localhost:3000/js/multiplayer.js:505:47)
    at http://localhost:3000/js/multiplayer.js:195:13
 [MATCH FOUND] Observer precedente disconnesso per nuova partita
 [MATCH FOUND] Applicando immediatamente stili online-play-interface
 [ONLINE ENHANCER] Applicazione IMMEDIATA della classe online-play-interface
 [ONLINE ENHANCER] IMMEDIATO: CSS di emergenza già presente nel file CSS
 [ONLINE ENHANCER] IMMEDIATO: Classe applicata alla players-column
 [ONLINE ENHANCER] IMMEDIATO: Classe applicata, stili gestiti dai CSS
 [MATCH FOUND] Creando slot per le carte nelle aree delle mani
 [HAND SLOTS] Creazione slot per le carte nelle aree delle mani
 [HAND SLOTS] Creati slot per la mano del giocatore 1
 [HAND SLOTS] Creati slot per la mano del giocatore 2
 [MATCH FOUND] myPlayerId impostato PRIMA del salvataggio nomi: SXcYhcjDerYA2bVjAAAX
 [MATCH FOUND] data.playerId: SXcYhcjDerYA2bVjAAAX socket.id: JatI2Twaf4mwsY0ZAAAf
 [MATCH FOUND] data.opponentId: gbyArfSOUKYehImeAAAd
 [MATCH FOUND] Usando playerId dal server: SXcYhcjDerYA2bVjAAAX
 [MATCH FOUND] Socket ID attuale: JatI2Twaf4mwsY0ZAAAf
 [MATCH FOUND] Nome utente corrente: giggio
 [MATCH FOUND] Nome avversario: bruscolino
 [MATCH FOUND] Il mio colore: black
 [MATCH FOUND] Dati match memorizzati: {"myName":"giggio","opponentName":"bruscolino","myColor":"black","opponentColor":"white"}
 [MATCH FOUND DEBUG] Salvato ME: ID=SXcYhcjDerYA2bVjAAAX, nome=giggio, colore=black
 [MATCH FOUND DEBUG] Salvato AVVERSARIO: ID=gbyArfSOUKYehImeAAAd, nome=bruscolino, colore=white
 [MATCH FOUND] Nomi permanenti salvati: {"SXcYhcjDerYA2bVjAAAX":"giggio","gbyArfSOUKYehImeAAAd":"bruscolino"}
 [MATCH FOUND] Colori permanenti salvati: {"SXcYhcjDerYA2bVjAAAX":"black","gbyArfSOUKYehImeAAAd":"white"}
 [MATCH FOUND] Nomi salvati permanentemente, delegando impostazione DOM a updatePlayerAreaNames
 [RATING] Rating avversario aggiornato nella UI: 1000
 [CHAT] Chat abilitata per il multiplayer
 [MATCH FOUND] Modalità online impostata
 [MATCH FOUND] myPlayerId già impostato in precedenza: SXcYhcjDerYA2bVjAAAX
 [GAME MODE] Manager inizializzato
 [MATCH FOUND] Inizializzazione game mode manager per partita online
 [GAME MODE] Avvio partita online
 [ONLINE GAME] Inizializzazione partita online
 [ONLINE GAME] Il mio ID: SXcYhcjDerYA2bVjAAAX
 [ONLINE GAME] Creazione slot per le carte nelle aree delle mani
 [HAND SLOTS] Creazione slot per le carte nelle aree delle mani
 [HAND SLOTS] Creati slot per la mano del giocatore 1
 [HAND SLOTS] Creati slot per la mano del giocatore 2
 [MATCH FOUND] Preparando interfaccia per partita online senza reset visuale
 [MATCH FOUND] Preparo container senza mostrarlo - sarà visualizzato quando arriva lo stato
 [MATCH FOUND] Container preparato ma nascosto per evitare refresh doppio
 [MATCH FOUND] Preparando setup senza mostrare interfaccia prematuramente
 [MATCH FOUND] Socket configurato: Object
 [MATCH FOUND] Entrando nella room del gioco con dati: Object
 [MATCH FOUND] Match trovato e processato: Object
 [MATCH FOUND] ===== FINE HANDLE MATCH FOUND =====
 [ONLINE ENHANCER] Rilevata modifica allo style del game-container
 [ONLINE ENHANCER] Rilevata modifica allo style del game-container
 Could not access stylesheet rules: 
(anonime) @ inspector.b9415ea5.js:12
 Ads initialization already in progress or completed
 Ads initialization already in progress or completed
 Ads initialization already in progress or completed
 
s7 @ inspector.b9415ea5.js:1
inspector.b9415ea5.js:1 Uncaught 
 [SMOOTH LOADING] Avvio animazioni di caricamento
 [SMOOTH LOADING] Animazioni di caricamento completate
 Attempting to initialize AdUnit
 AdUnit initialized successfully
 Ads initialized successfully for: http://localhost:3000/game
 Ads initialization already in progress or completed
 [ONLINE ENHANCER] Finestra completamente caricata
 [ONLINE ENHANCER] Interfaccia già applicata, evito ridondanza al caricamento
 [PERSISTENCE] Stato salvato scaduto o non presente
 [PERSISTENCE] Stato salvato rimosso
game:1 Uncaught (in promise) 
game:1 Uncaught (in promise) 
game:1 Uncaught (in promise) 
game:1 Uncaught (in promise) 
game:1 Uncaught (in promise) 
 [NAMES PROTECTION] Protezione attivata
 [MATCH FOUND] Richiesta stato del gioco con info complete: Object
 [SOCKET] Ricevuto evento gameState: Object
 [SOCKET] Chiamando handleGameStateEvent
 [GAME STATE] Ricevuto stato con proprietà: Array(25)
 [GAME STATE] currentPlayerId RAW from server: gbyArfSOUKYehImeAAAd
 [GAME STATE] diceResult: Object
 [GAME STATE] initialPosition: f2
 [GAME STATE] mode: online
 [GAME STATE DEBUG] players: {"gbyArfSOUKYehImeAAAd":{"id":"gbyArfSOUKYehImeAAAd","color":"white","handSize":5,"score":0,"hand":[{"id":"Paper-3","suit":"Paper","value":"3"},{"id":"Paper-8","suit":"Paper","value":"8"},{"id":"Rock-A","suit":"Rock","value":"A"},{"id":"Paper-9","suit":"Paper","value":"9"},{"id":"Paper-Q","suit":"Paper","value":"Q"}]},"SXcYhcjDerYA2bVjAAAX":{"id":"SXcYhcjDerYA2bVjAAAX","color":"black","handSize":5,"score":0,"hand":[{"id":"Rock-7","suit":"Rock","value":"7"},{"id":"Rock-Q","suit":"Rock","value":"Q"},{"id":"Rock-J","suit":"Rock","value":"J"},{"id":"Paper-2","suit":"Paper","value":"2"},{"id":"Rock-K","suit":"Rock","value":"K"}]}}
 [GAME STATE DEBUG] playerColors: {"gbyArfSOUKYehImeAAAd":"white","SXcYhcjDerYA2bVjAAAX":"black"}
 [GAME STATE DEBUG] playerNames: {"gbyArfSOUKYehImeAAAd":"bruscolino","SXcYhcjDerYA2bVjAAAX":"giggio"}
 [GAME STATE DEBUG] window.myPlayerId: SXcYhcjDerYA2bVjAAAX
 [VALIDATION] Controllo stato (36 board, 2 players)
 [VALIDATION] ✅ Stato valido - nessuna carta duplicata rilevata
 [ID SYNC] ID corretto confermato: SXcYhcjDerYA2bVjAAAX per username: giggio
 [SYNC DEBUG] Received board state: {"a1":null,"b1":null,"c1":null,"d1":null,"e1":null,"f1":null,"a2":null,"b2":null,"c2":null,"d2":null,"e2":null,"f2":{"suit":"Scissors","value":"J","id":"Scissors-J","ownerColor":"neutral"},"a3":null,"b3":null,"c3":null,"d3":null,"e3":null,"f3":null,"a4":null,"b4":null,"c4":null,"d4":null,"e4":null,"f4":null,"a5":null,"b5":null,"c5":null,"d5":null,"e5":null,"f5":null,"a6":null,"b6":null,"c6":null,"d6":null,"e6":null,"f6":null}
 [SYNC DEBUG] Board keys: Array(36)
 [IMMEDIATE NAMES] Skip aggiornamento nomi - già presenti nomi permanenti validi
 [IMMEDIATE NAMES] Nomi permanenti attuali: {"SXcYhcjDerYA2bVjAAAX":"giggio","gbyArfSOUKYehImeAAAd":"bruscolino"}
 [PLAYER AREAS DEBUG] Aggiornamento nomi chiamato
 [PLAYER AREAS DEBUG] effectiveState.playerColors: Object
 [PLAYER AREAS DEBUG] effectiveState.playerNames: Object
 [PLAYER AREAS DEBUG] namesLocked: false
 [PLAYER AREAS] Aggiorno aree giocatori in modalità online
 [PLAYER AREAS DEBUG] effectiveState.playerColors: {"SXcYhcjDerYA2bVjAAAX":"black","gbyArfSOUKYehImeAAAd":"white"}
 [PLAYER AREAS DEBUG] ID: SXcYhcjDerYA2bVjAAAX, Color: black
 [PLAYER AREAS DEBUG] ID: gbyArfSOUKYehImeAAAd, Color: white
 [PLAYER AREAS DEBUG] whitePlayerId: gbyArfSOUKYehImeAAAd, blackPlayerId: SXcYhcjDerYA2bVjAAAX
 [PLAYER AREAS DEBUG] Nomi permanenti disponibili: {"SXcYhcjDerYA2bVjAAAX":"giggio","gbyArfSOUKYehImeAAAd":"bruscolino"}
 [PLAYER AREAS DEBUG] Cerco nome per whitePlayerId: gbyArfSOUKYehImeAAAd
 [PLAYER AREAS DEBUG] Cerco nome per blackPlayerId: SXcYhcjDerYA2bVjAAAX
 [PLAYER AREAS DEBUG] Trovato nome permanente per bianco: bruscolino
 [PLAYER AREAS DEBUG] Trovato nome permanente per nero: giggio
 [PLAYER AREAS] Imposto nomi: bruscolino (bianco), giggio (nero)
 [PLAYER AREAS] player1-area riceverà: bruscolino (BIANCO)
 [PLAYER AREAS] player2-area riceverà: giggio (NERO)
 [PLAYER AREAS DOM] PRIMA dell'aggiornamento:
 [PLAYER AREAS DOM] player1NameElement.textContent = "..."
 [PLAYER AREAS DOM] player2NameElement.textContent = "..."
 [NAMES PROTECTION] Tentativo di modificare player1 textContent: bruscolino
 [NAMES PROTECTION] Tentativo di modificare player2 textContent: giggio
 [PLAYER AREAS DOM] DOPO l'aggiornamento:
 [PLAYER AREAS DOM] player1NameElement.textContent = "bruscolino"
 [PLAYER AREAS DOM] player2NameElement.textContent = "giggio"
 [PLAYER AREAS DOM] Impostato: player1="bruscolino" (bianco), player2="giggio" (nero)
 [PLAYER AREAS] Nomi definitivi bloccati: bianco=bruscolino, nero=giggio
 [PLAYER AREAS] namesFinallySet = true - Nessuna ulteriore modifica ai nomi sarà permessa
 [PLAYER AREAS DEBUG] currentPlayerId: gbyArfSOUKYehImeAAAd whitePlayerId: gbyArfSOUKYehImeAAAd blackPlayerId: SXcYhcjDerYA2bVjAAAX
 [PLAYER AREAS DEBUG] Aggiungendo current-player al giocatore bianco (player1)
 [PLAYER AREAS DEBUG] ✅ Aggiunta classe current-turn a player1-area per GLOW
 [PLAYER AREAS] Attivata animazione glow aggiungendo classe ready-for-play
 [PLAYER AREAS DEBUG] Post-applicazione classi:
 [PLAYER AREAS DEBUG] player1NameElement classes: player-name loaded current-player
 [PLAYER AREAS DEBUG] player2NameElement classes: player-name loaded
 [PLAYER AREAS DEBUG] Body ready-for-play: true
 [GAME STATE] isFirstStateReceived: true
 [GAME STATE] shouldShowDiceAnimation: f2
 [GAME STATE] Prima ricezione stato - Inizio animazione dadi
 [GAME STATE] showGameSetup già chiamato, skip
 [GAME STATE] Chiamando animateDiceRoll
 [DICE ANIMATION] animateDiceRoll chiamato - Animazione dei dadi commentata/disabilitata
 [DICE ANIMATION] Parametri: Object
 [DICE ANIM] Saltando animazione dadi, usando diceResult: Object e initialPosition: f2
 [DICE ANIM] Usando initialPosition (f2) -> Alpha: F, Numeric: 2
 [DICE ANIM] Dati dadi configurati:
 [DICE ANIM] Dado numerico mostrerà: 2
 [DICE ANIM] Dado alfabetico mostrerà: F
 [DICE ANIM] Dado colore mostrerà: white
 [DICE ANIM] initialPosition: f2
 [DICE ANIM] Saltata animazione dei dadi
 [DICE ANIM] Flag diceAnimationCompletedOnce impostato dopo salto animazione
 [ONLINE ENHANCER] Rilevata modifica allo style del game-container
 [ONLINE ENHANCER] Rilevata modifica allo style del game-container
 [GAME STATE] Animazione dadi completata
 [CHAT] TEST: Inviando messaggio alla chat
 [MATCH FOUND] Flag processingGameData pulito
 [GAME STATE] Preparando transizione fluida da setup a game container
 [GAME STATE] Preparazione interfaccia multiplayer con pre-rendering
 [GAME STATE] Creazione tabellone nascosto per multiplayer
 [BOARD CREATION] Creazione nuovo tabellone di gioco - Stack trace: Error
    at createGameBoard (http://localhost:3000/script.js:5179:87)
    at http://localhost:3000/script.js:11323:21
 [CELL LISTENER] Aggiungendo listener drag/drop a cella cell-a1
 [CELL LISTENER] Aggiungendo listener drag/drop a cella cell-b1
 [CELL LISTENER] Aggiungendo listener drag/drop a cella cell-c1
 [CELL LISTENER] Aggiungendo listener drag/drop a cella cell-d1
 [CELL LISTENER] Aggiungendo listener drag/drop a cella cell-e1
 [CELL LISTENER] Aggiungendo listener drag/drop a cella cell-f1
 [CELL LISTENER] Aggiungendo listener drag/drop a cella cell-a2
 [CELL LISTENER] Aggiungendo listener drag/drop a cella cell-b2
 [CELL LISTENER] Aggiungendo listener drag/drop a cella cell-c2
 [CELL LISTENER] Aggiungendo listener drag/drop a cella cell-d2
 [CELL LISTENER] Aggiungendo listener drag/drop a cella cell-e2
 [CELL LISTENER] Aggiungendo listener drag/drop a cella cell-f2
 [CELL LISTENER] Aggiungendo listener drag/drop a cella cell-a3
 [CELL LISTENER] Aggiungendo listener drag/drop a cella cell-b3
 [CELL LISTENER] Aggiungendo listener drag/drop a cella cell-c3
 [CELL LISTENER] Aggiungendo listener drag/drop a cella cell-d3
 [CELL LISTENER] Aggiungendo listener drag/drop a cella cell-e3
 [CELL LISTENER] Aggiungendo listener drag/drop a cella cell-f3
 [CELL LISTENER] Aggiungendo listener drag/drop a cella cell-a4
 [CELL LISTENER] Aggiungendo listener drag/drop a cella cell-b4
 [CELL LISTENER] Aggiungendo listener drag/drop a cella cell-c4
 [CELL LISTENER] Aggiungendo listener drag/drop a cella cell-d4
 [CELL LISTENER] Aggiungendo listener drag/drop a cella cell-e4
 [CELL LISTENER] Aggiungendo listener drag/drop a cella cell-f4
 [CELL LISTENER] Aggiungendo listener drag/drop a cella cell-a5
 [CELL LISTENER] Aggiungendo listener drag/drop a cella cell-b5
 [CELL LISTENER] Aggiungendo listener drag/drop a cella cell-c5
 [CELL LISTENER] Aggiungendo listener drag/drop a cella cell-d5
 [CELL LISTENER] Aggiungendo listener drag/drop a cella cell-e5
 [CELL LISTENER] Aggiungendo listener drag/drop a cella cell-f5
 [CELL LISTENER] Aggiungendo listener drag/drop a cella cell-a6
 [CELL LISTENER] Aggiungendo listener drag/drop a cella cell-b6
 [CELL LISTENER] Aggiungendo listener drag/drop a cella cell-c6
 [CELL LISTENER] Aggiungendo listener drag/drop a cella cell-d6
 [CELL LISTENER] Aggiungendo listener drag/drop a cella cell-e6
 [CELL LISTENER] Aggiungendo listener drag/drop a cella cell-f6
 [GAME STATE] Game container pre-renderizzato e pronto per fade-in
 [GAME STATE] Setup-animation non trovato
 [GAME STATE] Mostrando game container dopo preparazione
 [DICE ANIMATION] showGameContainer() chiamato
 [DICE ANIMATION] Nascondo dice animation area con fade-out
 [DICE ANIMATION] Nascondo dice animation overlay
 [SHOWGAME] Nomi già impostati definitivamente, preservo i valori attuali
 [SHOWGAME] Game container finale: Object
 [SHOWGAME DEBUG] Celle esistenti: 36 Dovrebbe creare tabellone: false
 [SHOWGAME] Tabellone già esistente, skip creazione
 [SHOWGAME] Partita multiplayer - evito resetGameUI per prevenire doppio refresh
 [SHOWGAME] Container multiplayer in attesa, skip visualizzazione
 [GAME STATE] Avvio animazioni carte dopo i dadi
 [NAMES PROTECTION] animateCardDealing intercettato
 [NAMES PROTECTION] Salvato nome player 1: bruscolino
 [NAMES PROTECTION] Salvato nome player 2: giggio
 [NAMES PROTECTION] Protezione attivata
 [NAMES PROTECTION] Salvato nome player 1: bruscolino
 [NAMES PROTECTION] Salvato nome player 2: giggio
 [CARD DEALING] Deck area mostrata
 [NAMES PROTECTION] Tentativo di modificare player1 textContent: 
 [NAMES PROTECTION] BLOCCATO svuotamento player1, mantengo: bruscolino
 [NAMES PROTECTION] Tentativo di modificare player2 textContent: 
 [NAMES PROTECTION] BLOCCATO svuotamento player2, mantengo: giggio
 [NAMES PROTECTION] renderHand intercettato, cards: 0
 [NAMES PROTECTION] Rilevato tentativo di render mano vuota, attivo protezione immediata
 [NAMES PROTECTION] Salvato nome player 1: bruscolino
 [NAMES PROTECTION] Salvato nome player 2: giggio
 [NAMES PROTECTION] Protezione attivata
 [NAMES PROTECTION] Salvato nome player 1: bruscolino
 [NAMES PROTECTION] Salvato nome player 2: giggio
 [RENDER HAND] Rendering 0 cards to player1-hand, isClickable: false
 [RENDER HAND] Cards: Array(0)
 [BRUSCOLINO RENDER DEBUG] Rendering mano player1-hand, isClickable: false
 [NAMES PROTECTION] renderHand intercettato, cards: 0
 [NAMES PROTECTION] Rilevato tentativo di render mano vuota, attivo protezione immediata
 [NAMES PROTECTION] Salvato nome player 1: bruscolino
 [NAMES PROTECTION] Salvato nome player 2: giggio
 [NAMES PROTECTION] Protezione attivata
 [NAMES PROTECTION] Salvato nome player 1: bruscolino
 [NAMES PROTECTION] Salvato nome player 2: giggio
 [RENDER HAND] Rendering 0 cards to player2-hand, isClickable: false
 [RENDER HAND] Cards: Array(0)
 [BRUSCOLINO RENDER DEBUG] Rendering mano player2-hand, isClickable: false
 [NAMES PROTECTION] Tentativo di modificare player1 textContent: bruscolino
 [NAMES PROTECTION] Tentativo di modificare player2 textContent: giggio
 [ONLINE ENHANCER] Rilevata modifica allo style del game-container
 [ONLINE ENHANCER] Rilevata modifica allo style del game-container
 [ONLINE ENHANCER] Rilevata modifica allo style del game-container
 [ONLINE ENHANCER] Rilevata modifica allo style del game-container
 [ONLINE ENHANCER] Rilevata modifica allo style del game-container
 [ONLINE ENHANCER] Rilevata modifica allo style del game-container
 [ONLINE ENHANCER] Rilevata modifica allo style del game-container
 [DECK IMAGE] Mazzo caricato con successo: http://localhost:3000/img/carte/card-back.webp
 [ANIMATION V2] Player 1, Card 0: SUCCESSO caricamento immagine: http://localhost:3000/img/carte/card-back.webp
 [ANIMATION V2] Image details: Object
 [FETCH TEST] Player 1, Card 0: Fetch test risultato: Object
 [ONLINE ENHANCER] Rilevata modifica allo style del game-container
 [ONLINE ENHANCER] Rilevata modifica allo style del game-container
 [FETCH TEST] Player 2, Card 0: Fetch test risultato: Object
 [FETCH TEST] Player 1, Card 1: Fetch test risultato: Object
 [FETCH TEST] Player 2, Card 1: Fetch test risultato: Object
 [FETCH TEST] Player 1, Card 2: Fetch test risultato: Object
 [FETCH TEST] Player 2, Card 2: Fetch test risultato: Object
 [FETCH TEST] Player 1, Card 3: Fetch test risultato: Object
 [FETCH TEST] Player 2, Card 3: Fetch test risultato: Object
 [FETCH TEST] Player 1, Card 4: Fetch test risultato: Object
 [FETCH TEST] Player 2, Card 4: Fetch test risultato: Object
 [ANIMATION V2] Player 1, Card 4: SUCCESSO caricamento immagine: http://localhost:3000/img/carte/card-back.webp
 [ANIMATION V2] Image details: Object
 [ANIMATION V2] Player 2, Card 4: SUCCESSO caricamento immagine: http://localhost:3000/img/carte/card-back.webp
 [ANIMATION V2] Image details: Object
 [ANIMATION V2] Player 2, Card 3: SUCCESSO caricamento immagine: http://localhost:3000/img/carte/card-back.webp
 [ANIMATION V2] Image details: Object
 [ANIMATION V2] Player 2, Card 0: SUCCESSO caricamento immagine: http://localhost:3000/img/carte/card-back.webp
 [ANIMATION V2] Image details: Object
 [ANIMATION V2] Player 2, Card 2: SUCCESSO caricamento immagine: http://localhost:3000/img/carte/card-back.webp
 [ANIMATION V2] Image details: Object
 [ANIMATION V2] Player 2, Card 1: SUCCESSO caricamento immagine: http://localhost:3000/img/carte/card-back.webp
 [ANIMATION V2] Image details: Object
 [ANIMATION V2] Player 1, Card 1: SUCCESSO caricamento immagine: http://localhost:3000/img/carte/card-back.webp
 [ANIMATION V2] Image details: Object
 [ANIMATION V2] Player 1, Card 2: SUCCESSO caricamento immagine: http://localhost:3000/img/carte/card-back.webp
 [ANIMATION V2] Image details: Object
 [ANIMATION V2] Player 1, Card 3: SUCCESSO caricamento immagine: http://localhost:3000/img/carte/card-back.webp
 [ANIMATION V2] Image details: Object
 [MULTIPLAYER] Dati di sessione rimossi dopo processamento
 [MATCH FOUND] Skip secondo tentativo: gameRunning= undefined hasInitialState= true
 [GAME STATE] Reset flag processingFirstOnlineState
 [VISIBILITY] Stato di visibilità cambiato: hidden
 [VISIBILITY] Setup interrotto da minimizzazione: true
 [VISIBILITY] Notificato al server: browser minimizzato
 [VISIBILITY] Pagina nascosta, preparo per riavvio animazioni
 [DICE STATUS DEBUG] Usando nomi permanenti: {"SXcYhcjDerYA2bVjAAAX":"giggio","gbyArfSOUKYehImeAAAd":"bruscolino"}
 [DICE STATUS DEBUG] Colori permanenti: {"SXcYhcjDerYA2bVjAAAX":"black","gbyArfSOUKYehImeAAAd":"white"}
 [DICE STATUS DEBUG] Dopo strategia 0 (permanente) - player1Name: bruscolino player2Name: giggio
 [DICE STATUS] Aggiornato stato dadi: F2 - Bianco: bruscolino, Nero: giggio
game:1 Uncaught (in promise) 
 [SOCKET] Ricevuto evento gameState: Object
 [SOCKET] Chiamando handleGameStateEvent
 [GAME STATE] Ricevuto stato con proprietà: Array(26)
 [GAME STATE] currentPlayerId RAW from server: SXcYhcjDerYA2bVjAAAX
 [GAME STATE] diceResult: Object
 [GAME STATE] initialPosition: f2
 [GAME STATE] mode: online
 [GAME STATE DEBUG] players: {"gbyArfSOUKYehImeAAAd":{"id":"gbyArfSOUKYehImeAAAd","color":"white","handSize":4,"score":0,"hand":[{"id":"Paper-3","suit":"Paper","value":"3"},{"id":"Paper-8","suit":"Paper","value":"8"},{"id":"Paper-9","suit":"Paper","value":"9"},{"id":"Paper-Q","suit":"Paper","value":"Q"}]},"SXcYhcjDerYA2bVjAAAX":{"id":"SXcYhcjDerYA2bVjAAAX","color":"black","handSize":5,"score":0,"hand":[{"id":"Rock-7","suit":"Rock","value":"7"},{"id":"Rock-Q","suit":"Rock","value":"Q"},{"id":"Rock-J","suit":"Rock","value":"J"},{"id":"Paper-2","suit":"Paper","value":"2"},{"id":"Rock-K","suit":"Rock","value":"K"}]}}
 [GAME STATE DEBUG] playerColors: {"gbyArfSOUKYehImeAAAd":"white","SXcYhcjDerYA2bVjAAAX":"black"}
 [GAME STATE DEBUG] playerNames: {"gbyArfSOUKYehImeAAAd":"bruscolino","SXcYhcjDerYA2bVjAAAX":"giggio"}
 [GAME STATE DEBUG] window.myPlayerId: SXcYhcjDerYA2bVjAAAX
 [VALIDATION] Controllo stato (36 board, 2 players)
 [ROLLBACK DEBUG] Controllo rollback rispetto allo stato precedente...
 [VALIDATION] ✅ Stato valido - nessuna carta duplicata rilevata
 [ID SYNC] ID corretto confermato: SXcYhcjDerYA2bVjAAAX per username: giggio
 [SYNC DEBUG] Received board state: {"a1":null,"b1":null,"c1":null,"d1":null,"e1":null,"f1":null,"a2":null,"b2":null,"c2":null,"d2":null,"e2":{"suit":"Rock","value":"A","id":"Rock-A","ownerColor":"white"},"f2":{"suit":"Scissors","value":"J","id":"Scissors-J","ownerColor":"neutral"},"a3":null,"b3":null,"c3":null,"d3":null,"e3":null,"f3":null,"a4":null,"b4":null,"c4":null,"d4":null,"e4":null,"f4":null,"a5":null,"b5":null,"c5":null,"d5":null,"e5":null,"f5":null,"a6":null,"b6":null,"c6":null,"d6":null,"e6":null,"f6":null}
 [SYNC DEBUG] Board keys: Array(36)
 [IMMEDIATE NAMES] Skip aggiornamento nomi - già presenti nomi permanenti validi
 [IMMEDIATE NAMES] Nomi permanenti attuali: {"SXcYhcjDerYA2bVjAAAX":"giggio","gbyArfSOUKYehImeAAAd":"bruscolino"}
 [PLAYER AREAS] Nomi definitivamente impostati, NON li tocco MAI
 [TURN INDICATOR] Aggiornamento solo indicatore turno
 [TURN INDICATOR] currentPlayerId ricevuto: SXcYhcjDerYA2bVjAAAX
 [TURN INDICATOR] Fallback - whitePlayerId: gbyArfSOUKYehImeAAAd blackPlayerId: SXcYhcjDerYA2bVjAAAX
 [TURN INDICATOR] ✅ ID originale nero rilevato - è il turno del giocatore NERO
 [TURN INDICATOR] ✅ Classe current-player aggiunta a Player2 (NERO)
 [TURN INDICATOR] ✅ Classe current-turn aggiunta a player2-area per GLOW
 [TURN INDICATOR DEBUG] Player1 classes: player-name loaded
 [TURN INDICATOR DEBUG] Player2 classes: player-name loaded current-player
 [TURN INDICATOR DEBUG] Body ready-for-play: true
 [TURN INDICATOR] 🎯 INDICATORE ATTIVO su Player2: giggio
 [TURN INDICATOR] === FINE AGGIORNAMENTO ===
 [GAME STATE] Partita online - continuo con flusso completo per garantire drag and drop
 [GAME STATE] isFirstStateReceived: false
 [GAME STATE] shouldShowDiceAnimation: false
 [PSN DEBUG] Controllo registrazione PSN (immediata)...
 [PSN DEBUG] window.PSN esistente: true
 [PSN DEBUG] registerMoveFromServer funzione: function
 [PSN AUTHORIRATIVE] Registrazione mosse dal server (immediata)
 [PSN SERVER] === REGISTRAZIONE AUTORITATIVA DAL SERVER ===
 [PSN SERVER] Prima ricezione stato server - controllo mosse esistenti
 [PSN SERVER] Prima ricezione - mossa reale rilevata: Rock-A in e2 (owner: white)
 [PSN SERVER] Prima ricezione - carte rimanenti per white (gbyArfSOUKYehImeAAAd): 4
 [PSN SERVER] Prima ricezione - registrazione di 1 mosse reali
 [PSN SERVER] Registrando mossa autoritativa: Rock-A in e2 (white, 4 carte rimanenti)
 [PSN DEBUG] Inizio registerMove: card={"suit":"Rock","value":"A","id":"Rock-A"}, position=e2 (mossa), color=mossa, gainedVertexControl=white, cardsRemaining=false, state.currentTurn (prima)=0, state.currentPlayer=white
 [PSN DEBUG RegisterMove Pre-Check] state.pendingVertexControl attuale: undefined
 [PSN DEBUG RegisterMove Pre-Check] state.pendingVertexControl è null o undefined.
 [PSN DEBUG] Dentro else (isWhite è false): state.currentTurn non modificato, resta 0
 [PSN DEBUG RegisterMove Pending] state.pendingVertexControl è vuoto o non definito al momento del check.
 [PSN DEBUG] Oggetto 'move' creato (dopo check pending): {"turnNumber":0,"isWhite":false,"card":{"suit":"Rock","value":"A","id":"Rock-A"},"position":"e2","gainedVertexControl":"white","cardsRemaining":false,"timestamp":"2025-06-26T19:46:42.560Z"}
 [PSN] Gioco iniziato - prima mossa registrata
 [PSN] Prima mossa rilevata, cambio a vista Notazioni.
 [PSN] Mossa registrata (con debug): Turno 0, Nero - Rock A su e2 (controllo vertice ottenuto). Prossimo giocatore: white. StateTurn: 0
 [PSN SERVER] Forzando aggiornamento griglia dopo prima ricezione con mosse
 [TURN PROTECTION] 🔒 MEMORIZZATO turno SOCKET ID ORIGINALE: SXcYhcjDerYA2bVjAAAX
 [TURN PROTECTION] Protezione attiva per 10 secondi
 [TURN PROTECTION] 🏁 Inizializzato wasInSetupPhase: true
 [DECK COUNTER DEBUG] gameState ricevuto con deckSize: 28
 === STATO TURNO RICEVUTO ===
 [TURN SYNC] currentPlayerId dal server: SXcYhcjDerYA2bVjAAAX
 [TURN SYNC] myPlayerId locale: SXcYhcjDerYA2bVjAAAX
 [TURN SYNC] window.myPlayerId (per mani): SXcYhcjDerYA2bVjAAAX
 [TURN SYNC] mySocketId (per socket): JatI2Twaf4mwsY0ZAAAf
 [TURN SYNC] effectiveMyPlayerId (per mani): SXcYhcjDerYA2bVjAAAX
 [TURN SYNC] effectiveTurnPlayerId (per turni): SXcYhcjDerYA2bVjAAAX
 [TURN SYNC] state.mode: online
 [TURN SYNC] È il mio turno? (CORRETTO): true
 [TURN SYNC] playerColors: Object
 [TURN SYNC] playerNames: Object
 [TURN DEBUG] === ANALISI DETTAGLIATA TURNO ONLINE ===
 [TURN DEBUG] currentPlayerId dal server: SXcYhcjDerYA2bVjAAAX
 [TURN DEBUG] mySocketId attuale: JatI2Twaf4mwsY0ZAAAf
 [TURN DEBUG] Confronto diretto: false
 [TURN DEBUG] Giocatori nello stato:
 [TURN DEBUG]   gbyArfSOUKYehImeAAAd (white): è currentPlayer? false
 [TURN DEBUG]   SXcYhcjDerYA2bVjAAAX (black): è currentPlayer? true
 [TURN DEBUG] isMyTurn dal game mode manager: false
 [TURN DEBUG] === FINE ANALISI ===
 ==========================
 [ONLINE GAME] Aggiornamento stato di gioco
 [ONLINE GAME] currentPlayerId BEFORE mapping: SXcYhcjDerYA2bVjAAAX
 [ONLINE GAME MAPPING] === INIZIO MAPPING DEBUG ===
 [ONLINE GAME MAPPING] state.players keys: Array(2)
 [ONLINE GAME MAPPING] this.myPlayerId: SXcYhcjDerYA2bVjAAAX
 [ONLINE GAME MAPPING] state.currentPlayerId: SXcYhcjDerYA2bVjAAAX
 [ONLINE GAME MAPPING] state.originalCurrentPlayerId (prima): undefined
 [ONLINE GAME MAPPING] === DEBUG MAPPING ===
 [ONLINE GAME MAPPING] this.myPlayerId: SXcYhcjDerYA2bVjAAAX
 [ONLINE GAME MAPPING] this.opponentId: gbyArfSOUKYehImeAAAd
 [ONLINE GAME MAPPING] state.currentPlayerId: SXcYhcjDerYA2bVjAAAX
 [ONLINE GAME MAPPING] localPlayerData: Object
 [ONLINE GAME MAPPING] opponentPlayerData: Object
 [ONLINE GAME MAPPING] Io sono NERO, assegno: player1=opponent, player2=me
 [ONLINE GAME MAPPING] 🔍 Verifica mani dopo mapping:
 [ONLINE GAME MAPPING] 🔍 player1.hand: Array(4)
 [ONLINE GAME MAPPING] 🔍 player2.hand: Array(5)
 [ONLINE GAME MAPPING] PRIMA trasformazione currentPlayerId: SXcYhcjDerYA2bVjAAAX
 [ONLINE GAME MAPPING] Controllo: currentPlayerId === myPlayerId? true
 [ONLINE GAME MAPPING] Controllo: currentPlayerId === opponentId? false
 [ONLINE GAME MAPPING] È il MIO turno, mio colore: black -> assegno currentPlayerId = player2
 [ONLINE GAME MAPPING] DOPO trasformazione currentPlayerId: player2
 [ONLINE GAME] Player mapping:
 [ONLINE GAME] - Player 1 (white): gbyArfSOUKYehImeAAAd
 [ONLINE GAME] - Player 2 (black): SXcYhcjDerYA2bVjAAAX
 [ONLINE GAME] - Current player ID transformed: player2
 [ONLINE GAME] - Original current player ID (socket): SXcYhcjDerYA2bVjAAAX
 [ONLINE GAME MAPPING] === FINE DEBUG MAPPING ===
 [ONLINE GAME] currentPlayerId AFTER mapping: player2
 [ONLINE GAME] originalCurrentPlayerId saved as: SXcYhcjDerYA2bVjAAAX
 [POST-MAPPING VALIDATION] Controllo carte duplicate dopo mapping online
 [POST-MAPPING VALIDATION] 📋 Board carta: Rock-A in e2
 [POST-MAPPING VALIDATION] 📋 Board carta: Scissors-J in f2
 [POST-MAPPING VALIDATION] 📋 Totale carte board: 2
 [POST-MAPPING VALIDATION] 📋 IDs board: Array(2)
 [POST-MAPPING VALIDATION] 📋 Cerco Rock-6 nel board: false
 [POST-MAPPING VALIDATION] 👤 Controllo mano player1: 4 carte
 [POST-MAPPING VALIDATION] 👤 Mano carta: Paper-3 (pos 0)
 [POST-MAPPING VALIDATION] 👤 Mano carta: Paper-8 (pos 1)
 [POST-MAPPING VALIDATION] 👤 Mano carta: Paper-9 (pos 2)
 [POST-MAPPING VALIDATION] 👤 Mano carta: Paper-Q (pos 3)
 [POST-MAPPING VALIDATION] 👤 Controllo mano player2: 5 carte
 [POST-MAPPING VALIDATION] 👤 Mano carta: Rock-7 (pos 0)
 [POST-MAPPING VALIDATION] 👤 Mano carta: Rock-Q (pos 1)
 [POST-MAPPING VALIDATION] 👤 Mano carta: Rock-J (pos 2)
 [POST-MAPPING VALIDATION] 👤 Mano carta: Paper-2 (pos 3)
 [POST-MAPPING VALIDATION] 👤 Mano carta: Rock-K (pos 4)
 [POST-MAPPING VALIDATION] 🎯 ROCK-K TROVATO IN MANO di player2!
(anonime) @ script.js:12044
 [POST-MAPPING VALIDATION] 🎯 Sul board? false
(anonime) @ script.js:12045
 [POST-MAPPING VALIDATION] 🎯 Posizione mano: 4
(anonime) @ script.js:12046
 [POST-MAPPING VALIDATION] 🎯 Board contents: Array(2)
(anonime) @ script.js:12047
 [POST-MAPPING VALIDATION] ✅ Nessun duplicato post-mapping rilevato
 [FINAL VALIDATION] Controllo finale forzato per carte duplicate...
 [FINAL VALIDATION] ✅ Nessuna correzione finale necessaria
 [GAME STATE] Stato mani dopo aggiornamento:
 [GAME STATE] player1 (white): Array(4)
 [GAME STATE] player2 (black): Array(5)
 [PLAYER AREAS] Nomi definitivamente impostati, NON li tocco MAI
 [TURN INDICATOR] Aggiornamento solo indicatore turno
 [TURN INDICATOR] currentPlayerId ricevuto: player2
 [TURN INDICATOR] ✅ ID mappato player2 rilevato - è il turno del giocatore NERO
 [TURN INDICATOR] ✅ Classe current-player aggiunta a Player2 (NERO)
 [TURN INDICATOR] ✅ Classe current-turn aggiunta a player2-area per GLOW
 [TURN INDICATOR DEBUG] Player1 classes: player-name loaded
 [TURN INDICATOR DEBUG] Player2 classes: player-name loaded current-player
 [TURN INDICATOR DEBUG] Body ready-for-play: true
 [TURN INDICATOR] 🎯 INDICATORE ATTIVO su Player2: giggio
 [TURN INDICATOR] === FINE AGGIORNAMENTO ===
 [PERMANENT NAMES DEBUG] Server playerNames: {"gbyArfSOUKYehImeAAAd":"bruscolino","SXcYhcjDerYA2bVjAAAX":"giggio"}
 [PERMANENT NAMES DEBUG] Attuali permanentPlayerNames: {"SXcYhcjDerYA2bVjAAAX":"giggio","gbyArfSOUKYehImeAAAd":"bruscolino"}
 [PERMANENT NAMES] Mantenuto nome esistente per gbyArfSOUKYehImeAAAd: bruscolino
 [PERMANENT NAMES] Mantenuto nome esistente per SXcYhcjDerYA2bVjAAAX: giggio
 [PERMANENT NAMES] Nomi permanenti finali: {"SXcYhcjDerYA2bVjAAAX":"giggio","gbyArfSOUKYehImeAAAd":"bruscolino"}
 [PERMANENT COLORS DEBUG] Server playerColors: {"gbyArfSOUKYehImeAAAd":"white","SXcYhcjDerYA2bVjAAAX":"black"}
 [PERMANENT COLORS DEBUG] Attuali permanentPlayerColors: {"SXcYhcjDerYA2bVjAAAX":"black","gbyArfSOUKYehImeAAAd":"white"}
 [PERMANENT COLORS] Confermato colore per gbyArfSOUKYehImeAAAd: white
 [PERMANENT COLORS] Confermato colore per SXcYhcjDerYA2bVjAAAX: black
 [PERMANENT COLORS] Colori permanenti finali: {"SXcYhcjDerYA2bVjAAAX":"black","gbyArfSOUKYehImeAAAd":"white"}
 [PSN SYNC] Aggiornamento handSize nel PSN dai dati del server
 [PSN API] Aggiornamento handSize dai dati del server
 [PSN API] Aggiornato handSize bianco: 4
 [PSN API] Aggiornato handSize nero: 5
 [EARLY PLAYER2 MONITOR] Observer installato il prima possibile, stato attuale: giggio
 [EARLY RESTORE] Controllo nomi: Object
 [GAME STATE DEBUG] Entrando nel ramo else if per updateGameUI - isOnline: true
 [GAME STATE DEBUG] Chiamando updateGameUI dal ramo else if
 [UPDATE UI DEBUG 1] PRIMA di currentGameState = state:
 [UPDATE UI DEBUG 1] - state.originalCurrentPlayerId: SXcYhcjDerYA2bVjAAAX
 [UPDATE UI DEBUG 1] - previousOriginalCurrentPlayerId: SXcYhcjDerYA2bVjAAAX
 [UPDATE UI] originalCurrentPlayerId già presente nello stato: SXcYhcjDerYA2bVjAAAX
 [UPDATE UI DEBUG 1] DOPO aggiornamento:
 [UPDATE UI DEBUG 1] - currentGameState.originalCurrentPlayerId: SXcYhcjDerYA2bVjAAAX
 [ADVANTAGE] Calcolo vantaggio con stato: {"vertex-a1":null,"vertex-f1":null,"vertex-a6":null,"vertex-f6":null}
 [ADVANTAGE] Componenti: Vertici (0-0), Carte (0-0), Adiacenze (0-0)
 [ADVANTAGE] Valore vantaggio calcolato: 0
 [ADVANTAGE] Percentuale finale: 50.0%
 [DEBUG] Vantaggio calcolato: 50%
 [HISTORY] Aggiunta mossa #1 alla cronologia
 Tentativo di forzare rendering rating...
 Ratings dopo init: Object
 Aggiornamento avatar player1: player1
 Aggiornamento avatar player2: player2
 [GIOCA TAB] PROTEZIONE TURNO ATTIVA - Uso ID protetto: SXcYhcjDerYA2bVjAAAX
 [GIOCA TAB] Modalità online - originalTurnPlayerId: SXcYhcjDerYA2bVjAAAX window.myPlayerId: SXcYhcjDerYA2bVjAAAX isMyTurn: true
 [ONLINE UI] Nomi basati sui colori: P1 (BIANCO)=bruscolino, P2 (NERO)=giggio
 [UI] Nomi predefiniti: P1=bruscolino, P2=giggio
 [UPDATE UI DEBUG 2] SECONDA ASSEGNAZIONE - PRIMA di currentGameState = state:
 [UPDATE UI DEBUG 2] - state.originalCurrentPlayerId: SXcYhcjDerYA2bVjAAAX
 [UPDATE UI DEBUG 2] - previousOriginalCurrentPlayerId: SXcYhcjDerYA2bVjAAAX
 [UPDATE UI DEBUG 2] - currentGameState.originalCurrentPlayerId prima: SXcYhcjDerYA2bVjAAAX
 [UPDATE UI FINAL] originalCurrentPlayerId già presente nello stato: SXcYhcjDerYA2bVjAAAX
 [UPDATE UI DEBUG 2] SECONDA ASSEGNAZIONE - DOPO aggiornamento:
 [UPDATE UI DEBUG 2] - currentGameState.originalCurrentPlayerId: SXcYhcjDerYA2bVjAAAX
 [PERSISTENCE] Salvataggio stato partita...
 [PERSISTENCE] Stato salvato: Object
 [ONLINE UI] INIZIO modalità online - player1/player2 già mappati da online-game.js
 [ONLINE UI] Identificato: io sono PLAYER2 (nero)
 [ONLINE UI] PROTEZIONE TURNO ATTIVA durante setup - Uso ID protetto: SXcYhcjDerYA2bVjAAAX
 [ONLINE UI] Debug turno DETTAGLIATO:
 [ONLINE UI] - state.originalCurrentPlayerId: SXcYhcjDerYA2bVjAAAX tipo: string
 [ONLINE UI] - effectiveCurrentPlayerId (usato): SXcYhcjDerYA2bVjAAAX
 [ONLINE UI] - window.myPlayerId: SXcYhcjDerYA2bVjAAAX tipo: string
 [ONLINE UI] - Confronto diretto (===): true
 [ONLINE UI] - Confronto == : true
 [ONLINE UI] - effectiveCurrentPlayerId presente?: true
 [ONLINE UI] - window.myPlayerId presente?: true
 [ONLINE UI] - isMyTurn: true
 [ONLINE UI] - isPlayer1Local: false isPlayer1Turn: false
 [ONLINE UI] - isPlayer2Local: true isPlayer2Turn: true
 [NAMES PROTECTION] renderHand intercettato, cards: 4
 [RENDER HAND] Rendering 4 cards to player1-hand, isClickable: false
 [RENDER HAND] Cards: Array(4)
 [BRUSCOLINO RENDER DEBUG] Rendering mano player1-hand, isClickable: false
 [HAND DEBUG] player1-hand: isClickable=false, handLength=4, isOpponentHand=true
 [OPPONENT HAND] player1-hand: previousSize=null, currentSize=4, hasPlayedCard=false
 [CARD DRAG] NON trascinabile carta Paper-3 in player1-hand - isClickable:false, suit:Paper, isLastCard:false, isReplayingMove:false, isSetupAnimating:false
 [BRUSCOLINO CARD DEBUG] NON ABILITO drag per carta Paper-3 in player1-hand - motivo: isClickable=false, suit=Paper, isLastCard=false, isReplayingMove=false, isSetupAnimating=false
 [CARD DRAG] NON trascinabile carta Paper-8 in player1-hand - isClickable:false, suit:Paper, isLastCard:false, isReplayingMove:false, isSetupAnimating:false
 [BRUSCOLINO CARD DEBUG] NON ABILITO drag per carta Paper-8 in player1-hand - motivo: isClickable=false, suit=Paper, isLastCard=false, isReplayingMove=false, isSetupAnimating=false
 [CARD DRAG] NON trascinabile carta Paper-9 in player1-hand - isClickable:false, suit:Paper, isLastCard:false, isReplayingMove:false, isSetupAnimating:false
 [BRUSCOLINO CARD DEBUG] NON ABILITO drag per carta Paper-9 in player1-hand - motivo: isClickable=false, suit=Paper, isLastCard=false, isReplayingMove=false, isSetupAnimating=false
 [CARD DRAG] NON trascinabile carta Paper-Q in player1-hand - isClickable:false, suit:Paper, isLastCard:false, isReplayingMove:false, isSetupAnimating:false
 [BRUSCOLINO CARD DEBUG] NON ABILITO drag per carta Paper-Q in player1-hand - motivo: isClickable=false, suit=Paper, isLastCard=false, isReplayingMove=false, isSetupAnimating=false
 [NAMES PROTECTION] renderHand intercettato, cards: 5
 [RENDER HAND] Rendering 5 cards to player2-hand, isClickable: true
 [RENDER HAND] Cards: Array(5)
 [BRUSCOLINO RENDER DEBUG] Rendering mano player2-hand, isClickable: true
 [HAND DEBUG] player2-hand: isClickable=true, handLength=5, isOpponentHand=false
 [CARD DRAG] Rendendo trascinabile carta Rock-7 in player2-hand
 [CARD DRAG] Verificando listener su elemento: 
 [BRUSCOLINO CARD DEBUG] ABILITO drag per carta Rock-7 in player2-hand
 [CARD DRAG] Listener aggiunti. Draggable: true, Cursor: grab
 [CARD DRAG] Rendendo trascinabile carta Rock-Q in player2-hand
 [CARD DRAG] Verificando listener su elemento: 
 [BRUSCOLINO CARD DEBUG] ABILITO drag per carta Rock-Q in player2-hand
 [CARD DRAG] Listener aggiunti. Draggable: true, Cursor: grab
 [CARD DRAG] Rendendo trascinabile carta Rock-J in player2-hand
 [CARD DRAG] Verificando listener su elemento: 
 [BRUSCOLINO CARD DEBUG] ABILITO drag per carta Rock-J in player2-hand
 [CARD DRAG] Listener aggiunti. Draggable: true, Cursor: grab
 [CARD DRAG] Rendendo trascinabile carta Paper-2 in player2-hand
 [CARD DRAG] Verificando listener su elemento: 
 [BRUSCOLINO CARD DEBUG] ABILITO drag per carta Paper-2 in player2-hand
 [CARD DRAG] Listener aggiunti. Draggable: true, Cursor: grab
 [CARD DRAG] Rendendo trascinabile carta Rock-K in player2-hand
 [CARD DRAG] Verificando listener su elemento: 
 [BRUSCOLINO CARD DEBUG] ABILITO drag per carta Rock-K in player2-hand
 [CARD DRAG] Listener aggiunti. Draggable: true, Cursor: grab
 [DEBUG] updateGameUI BEFORE renderBoard - boardArea.offsetHeight: 1261
 [DEBUG] updateGameUI BEFORE renderBoard - gameBoardElement.offsetHeight: 1224
 [DEBUG] renderBoard START - gameBoardElement.innerHTML (inizio): <div class="cell" id="cell-a1" data-row="1" data-col="a" data-notation="a1" data-listeners-added="tr...
 [DEBUG] renderBoard - Animazioni completate e tabellone esistente, uso updateBoardCardsOnly
 [BOARD DATA ONLY] Aggiornamento carte tabellone
 [BOARD DATA ONLY] Parametri ricevuti: Object
 [BOARD DATA ONLY] Inizio ciclo aggiornamento carte per 36 posizioni
 [BOARD DATA ONLY] Processando carta in e2: Object
 [BOARD DATA ONLY] Creando nuova carta in e2 per: Object
 [BOARD DATA ONLY] Carta aggiunta al DOM in e2
 [BOARD DATA ONLY] Processando carta in f2: Object
 [BOARD DATA ONLY] Creando nuova carta in f2 per: Object
 [BOARD DATA ONLY] Carta aggiunta al DOM in f2
 [DEBUG] updateGameUI AFTER renderBoard
 [DEBUG] updateGameUI AFTER renderBoard - gameBoardElement.offsetHeight: 1224
 [DEBUG] updateGameUI AFTER renderBoard - boardArea.offsetHeight: 1261
 [DECK COUNTER DEBUG] updateGameUI con state.deckSize: 28 (tipo: number)
 [DECK COUNTER DEBUG] Chiamando renderDeck con validDeckSize: 28
 [DECK COUNTER DEBUG] renderDeck chiamato con remainingCards: 28
 [DECK COUNTER DEBUG] Valori: oldCounterValue=39, newCounterValue=28
 [DECK COUNTER DEBUG] Contatore aggiornato a: 28
 [RIBALTONE UI] Controllo messaggio ribaltone. state.ribaltoneMessage: non presente
 [RIBALTONE UI] Controllo messaggio ribaltone. state.continueForOneTurn: false
 [GIOCA TAB] PROTEZIONE TURNO ATTIVA - Uso ID protetto: SXcYhcjDerYA2bVjAAAX
 [GIOCA TAB] Modalità online - originalTurnPlayerId: SXcYhcjDerYA2bVjAAAX window.myPlayerId: SXcYhcjDerYA2bVjAAAX isMyTurn: true
 [TURN TIMER] Chiamando updateGameStateWithTurnTimer per partita online
 [TURN TIMER DEBUG] === ANALISI CONTROLLO TURNO ===
 [TURN TIMER DEBUG] gameState.currentPlayerId: player2
 [TURN TIMER DEBUG] gameState.originalCurrentPlayerId: SXcYhcjDerYA2bVjAAAX
 [TURN TIMER DEBUG] gameState.myPlayerId: JatI2Twaf4mwsY0ZAAAf
 [TURN TIMER DEBUG] window.myPlayerId: SXcYhcjDerYA2bVjAAAX
 [TURN TIMER DEBUG] window.socket?.id: JatI2Twaf4mwsY0ZAAAf
 [TURN TIMER DEBUG] mySocketId: JatI2Twaf4mwsY0ZAAAf
 [TURN TIMER DEBUG] isMyTurn (CORRETTO): true
 [TURN TIMER DEBUG] === FINE ANALISI ===
 [TIMER] Avvio timer di turno: 59 secondi rimanenti
 [TIMER] Avviato timer totale per giocatore 2
 [GAMESTATE] Rating giocatore 2 aggiornato: 1000
 [PSN] Usando handSize dal server per white: 4 carte rimanenti
 [PSN CAPTURE] Modalità multiplayer - NON registro mossa (gestita dal server)
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [VISIBILITY] Stato di visibilità cambiato: visible
 [VISIBILITY] Notificato al server: browser tornato visibile
 [VISIBILITY] Setup era stato interrotto - applicando correzioni specifiche
 [VISIBILITY] Pagina tornata visibile, controllo animazioni in sospeso
 [VISIBILITY] Eseguo pulizia cover cards bloccate...
 [VISIBILITY] Trovate 5 cover cards bloccate, le rimuovo
 [VISIBILITY] Cover card rimossa
 [VISIBILITY] Cover card rimossa
 [VISIBILITY] Cover card rimossa
 [VISIBILITY] Cover card rimossa
 [VISIBILITY] Cover card rimossa
 [PSN] Browser tornato visibile - controllo sincronizzazione PSN
 [ANIMATION] Tutte le animazioni di distribuzione carte completate
 [ANIM] Avvio animazione carta iniziale: Object su f2
 [ANIM] Partita online rilevata - Controllo se devo saltare animazione
 [ANIM] window.myPlayerId: SXcYhcjDerYA2bVjAAAX
 [ANIM] window.permanentPlayerColors: Object
 [ANIM] initialCard: Object
 [ANIM] initialPosition: f2
 [ANIM] Il mio colore: black
 [ANIM] CONTROLLO 1 - Carta non ha ownerColor valido: neutral
 [ANIM] CONTROLLO 2 - Carta esistente nel board state: Object
 [ANIM] CONTROLLO 2 - existingCard.ownerColor: neutral vs mio colore: black
 [ANIM] CONTROLLO 2 - Carta board state è neutral o del mio colore, continuo
 [ANIM] CONTROLLO 3 - originalCurrentPlayerId: SXcYhcjDerYA2bVjAAAX vs myPlayerId: SXcYhcjDerYA2bVjAAAX
 [ANIM] CONTROLLO 3 - è il mio turno: true initialCard.ownerColor: neutral
 [ANIM] CONTROLLO 3 - È il mio turno o carta neutral, continuo
 [ANIM] CONTROLLO 4 - lastMovePlayerId non disponibile nel currentGameState
 [ANIM] CONTROLLO FINALE - isMyTurnCurrently: true
 [ANIM] Continuo con animazione normale - tutti i controlli passati
 [ANIM] La carta è già presente nella cella f2, annullo animazione
 [ANIMATION DEBUG] Verifica condizioni per animazione nomi: Object
 [ANIMATION] Animando con G1 (Bianco): bruscolino vs G2 (Nero): giggio
 [ANIMATION] Convenzione standard: Bianco in alto, Nero in basso
 [SOCKET] Ricevuto evento gameState: Object
script.js:1171 [SOCKET] Chiamando handleGameStateEvent
script.js:10799 [GAME STATE] Ricevuto stato con proprietà: Array(25)
script.js:10800 [GAME STATE] currentPlayerId RAW from server: SXcYhcjDerYA2bVjAAAX
script.js:10801 [GAME STATE] diceResult: Object
script.js:10802 [GAME STATE] initialPosition: f2
script.js:10803 [GAME STATE] mode: online
script.js:10804 [GAME STATE DEBUG] players: {"gbyArfSOUKYehImeAAAd":{"id":"gbyArfSOUKYehImeAAAd","color":"white","handSize":4,"score":0,"hand":[{"id":"Paper-3","suit":"Paper","value":"3"},{"id":"Paper-8","suit":"Paper","value":"8"},{"id":"Paper-9","suit":"Paper","value":"9"},{"id":"Paper-Q","suit":"Paper","value":"Q"}]},"SXcYhcjDerYA2bVjAAAX":{"id":"SXcYhcjDerYA2bVjAAAX","color":"black","handSize":5,"score":0,"hand":[{"id":"Rock-7","suit":"Rock","value":"7"},{"id":"Rock-Q","suit":"Rock","value":"Q"},{"id":"Rock-J","suit":"Rock","value":"J"},{"id":"Paper-2","suit":"Paper","value":"2"},{"id":"Rock-K","suit":"Rock","value":"K"}]}}
script.js:10805 [GAME STATE DEBUG] playerColors: {"gbyArfSOUKYehImeAAAd":"white","SXcYhcjDerYA2bVjAAAX":"black"}
script.js:10806 [GAME STATE DEBUG] playerNames: {"gbyArfSOUKYehImeAAAd":"bruscolino","SXcYhcjDerYA2bVjAAAX":"giggio"}
script.js:10807 [GAME STATE DEBUG] window.myPlayerId: SXcYhcjDerYA2bVjAAAX
script.js:10822 [VALIDATION] Controllo stato (36 board, 2 players)
script.js:10851 [ROLLBACK DEBUG] Controllo rollback rispetto allo stato precedente...
script.js:11115 [VALIDATION] ✅ Stato valido - nessuna carta duplicata rilevata
script.js:11140 [GAME STATE] Stato già elaborato, skip elaborazione duplicata
script.js:1554 [SOCKET] Cambio di visibilità del client _e_15r2d2p5JOpsSAAAh: hidden  
script.js:1560 [SOCKET] Un altro client è minimizzato, pronto a gestire le sue animazioni se necessario
script.js:8775 [PLAYER NAMES ANIMATION] ===== FUNZIONE CHIAMATA =====
script.js:8776 [PLAYER NAMES ANIMATION] Avvio animazione nomi giocatori DIRETTA
script.js:8777 [PLAYER NAMES ANIMATION] Nomi forniti: bruscolino vs giggio
script.js:8785 [PLAYER NAMES ANIMATION] Usando nomi forniti direttamente: bruscolino vs giggio
script.js:8820 [PLAYER NAMES ANIMATION] Nomi giocatori finali: bruscolino vs giggio
script.js:8824 [PLAYER NAMES ANIMATION] Game board trovato: true
script.js:8825 [PLAYER NAMES ANIMATION] Game board dimensioni: DOMRect
script.js:8861 [AVATAR DEBUG] Cercando avatar per bruscolino per posizione top
script.js:8901 [AVATAR DEBUG] Livello determinato per bruscolino: Principiante
script.js:8941 [AVATAR DEBUG] Tentativo di caricamento avatar da: http://localhost:3000/img/avatar/Principiante.webp
script.js:8945 [AVATAR DEBUG] Avatar trovato nella cache: http://localhost:3000/img/avatar/Principiante.webp
script.js:8861 [AVATAR DEBUG] Cercando avatar per giggio per posizione bottom
script.js:8901 [AVATAR DEBUG] Livello determinato per giggio: Principiante
script.js:8941 [AVATAR DEBUG] Tentativo di caricamento avatar da: http://localhost:3000/img/avatar/Principiante.webp
script.js:8945 [AVATAR DEBUG] Avatar trovato nella cache: http://localhost:3000/img/avatar/Principiante.webp
script.js:9335 [PLAYER NAMES ANIMATION] Animazione creata dinamicamente e aggiunta al DOM
script.js:9336 [PLAYER NAMES ANIMATION] Container aggiunto al game-board: dynamic-names-animation
script.js:9337 [PLAYER NAMES ANIMATION] Game-board children count: 41
script.js:8995 [AVATAR DEBUG] Avatar player1 caricato con successo
script.js:9110 [AVATAR DEBUG] Avatar player2 caricato con successo
script.js:2220 [VISIBILITY] Richiesta stato aggiornato consolidata per setup interrotto
script.js:2232 [VISIBILITY] Flag setup interrotto resettato
script.js:1167 [SOCKET] Ricevuto evento gameState: Object
script.js:1171 [SOCKET] Chiamando handleGameStateEvent
script.js:10799 [GAME STATE] Ricevuto stato con proprietà: Array(25)
script.js:10800 [GAME STATE] currentPlayerId RAW from server: SXcYhcjDerYA2bVjAAAX
script.js:10801 [GAME STATE] diceResult: Object
script.js:10802 [GAME STATE] initialPosition: f2
script.js:10803 [GAME STATE] mode: online
script.js:10804 [GAME STATE DEBUG] players: {"gbyArfSOUKYehImeAAAd":{"id":"gbyArfSOUKYehImeAAAd","color":"white","handSize":4,"score":0,"hand":[{"id":"Paper-3","suit":"Paper","value":"3"},{"id":"Paper-8","suit":"Paper","value":"8"},{"id":"Paper-9","suit":"Paper","value":"9"},{"id":"Paper-Q","suit":"Paper","value":"Q"}]},"SXcYhcjDerYA2bVjAAAX":{"id":"SXcYhcjDerYA2bVjAAAX","color":"black","handSize":5,"score":0,"hand":[{"id":"Rock-7","suit":"Rock","value":"7"},{"id":"Rock-Q","suit":"Rock","value":"Q"},{"id":"Rock-J","suit":"Rock","value":"J"},{"id":"Paper-2","suit":"Paper","value":"2"},{"id":"Rock-K","suit":"Rock","value":"K"}]}}
script.js:10805 [GAME STATE DEBUG] playerColors: {"gbyArfSOUKYehImeAAAd":"white","SXcYhcjDerYA2bVjAAAX":"black"}
script.js:10806 [GAME STATE DEBUG] playerNames: {"gbyArfSOUKYehImeAAAd":"bruscolino","SXcYhcjDerYA2bVjAAAX":"giggio"}
script.js:10807 [GAME STATE DEBUG] window.myPlayerId: SXcYhcjDerYA2bVjAAAX
script.js:10822 [VALIDATION] Controllo stato (36 board, 2 players)
script.js:10851 [ROLLBACK DEBUG] Controllo rollback rispetto allo stato precedente...
script.js:11115 [VALIDATION] ✅ Stato valido - nessuna carta duplicata rilevata
script.js:11140 [GAME STATE] Stato già elaborato, skip elaborazione duplicata
psn-unified.js:882 [PSN] Mosse presenti dopo ritorno visibilità - forza sincronizzazione
psn-unified.js:915 [PSN] Sistema PSN standard non disponibile - cattura stato corrente
psn-unified.js:1848 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
psn-unified.js:1848 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
script.js:9341 [PLAYER NAMES ANIMATION] Terminando animazione
script.js:9347 [PLAYER NAMES ANIMATION] Avvio fade-in immediato del game container
script.js:9352 [PLAYER NAMES ANIMATION] Fade-in avviato immediatamente
script.js:9380 [PLAYER NAMES ANIMATION] Animazione completata e rimossa
script.js:9381 [PLAYER NAMES ANIMATION] Fade-in completato
script.js:9389 [PLAYER NAMES ANIMATION] isSetupAnimating resettato a false alla fine reale dell'animazione
script.js:9390 [PLAYER NAMES ANIMATION] nameAnimationCompleted impostato a true - animazione nomi completata
script.js:9393 [PLAYER NAMES ANIMATION] Forzando re-rendering delle carte dopo reset isSetupAnimating...
script.js:778 [SETUP RE-RENDER] Forzando re-rendering delle carte dopo fine setup...
script.js:805 [SETUP RE-RENDER] Player player1 (white): isLocal=false, isMyTurn=true, clickable=false
script.js:806 [SETUP RE-RENDER] originalCurrentPlayerId: SXcYhcjDerYA2bVjAAAX, myPlayerId: SXcYhcjDerYA2bVjAAAX
script.js:807 [SETUP RE-RENDER] myColor: black, playerColor: white
script.js:823 [SETUP RE-RENDER] Re-rendering mano white (player1), clickable: false
script.js:824 [SETUP RE-RENDER] Carte esistenti nel DOM: 4
multiplayer.js:1795 [ERROR] Errore JavaScript rilevato: ReferenceError: handleCardDragStart is not defined
    at script.js:848:66
    at NodeList.forEach (<anonymous>)
    at script.js:827:27
    at Array.forEach (<anonymous>)
    at forceReRenderCardsAfterSetup (script.js:784:43)
    at script.js:9395:21
(anonime) @ multiplayer.js:1795
multiplayer.js:1796 [ERROR] Messaggio: Uncaught ReferenceError: handleCardDragStart is not defined
(anonime) @ multiplayer.js:1796
multiplayer.js:1797 [ERROR] File: http://localhost:3000/script.js
(anonime) @ multiplayer.js:1797
multiplayer.js:1798 [ERROR] Linea: 848
(anonime) @ multiplayer.js:1798
multiplayer.js:1799 [ERROR] Stack: ReferenceError: handleCardDragStart is not defined
    at http://localhost:3000/script.js:848:66
    at NodeList.forEach (<anonymous>)
    at http://localhost:3000/script.js:827:27
    at Array.forEach (<anonymous>)
    at forceReRenderCardsAfterSetup (http://localhost:3000/script.js:784:43)
    at http://localhost:3000/script.js:9395:21
(anonime) @ multiplayer.js:1799
script.js:848 Uncaught ReferenceError: handleCardDragStart is not defined
    at script.js:848:66
    at NodeList.forEach (<anonymous>)
    at script.js:827:27
    at Array.forEach (<anonymous>)
    at forceReRenderCardsAfterSetup (script.js:784:43)
    at script.js:9395:21
player-names-protection.js:141 [NAMES PROTECTION] Protezione disattivata - nomi presenti
player-names-protection.js:141 [NAMES PROTECTION] Protezione disattivata - nomi presenti
player-names-protection.js:141 [NAMES PROTECTION] Protezione disattivata - nomi presenti
player-names-protection.js:141 [NAMES PROTECTION] Protezione disattivata - nomi presenti
script.js:2162 [VISIBILITY] Stato di visibilità cambiato: hidden
script.js:2178 [VISIBILITY] Setup interrotto da minimizzazione: true
script.js:2186 [VISIBILITY] Notificato al server: browser minimizzato
script.js:2197 [VISIBILITY] Salvando lo stato originale delle mani dei giocatori
script.js:2506 [VISIBILITY] Pagina nascosta, preparo per riavvio animazioni
script.js:2162 [VISIBILITY] Stato di visibilità cambiato: visible
script.js:2211 [VISIBILITY] Notificato al server: browser tornato visibile
script.js:2215 [VISIBILITY] Setup era stato interrotto - applicando correzioni specifiche
script.js:2238 [VISIBILITY] Browser tornato visibile, ripristino stato originale delle mani
script.js:2304 [VISIBILITY] Pagina tornata visibile, controllo animazioni in sospeso
script.js:2307 [VISIBILITY] Eseguo pulizia cover cards bloccate...
script.js:2320 [VISIBILITY] Nessuna cover card bloccata trovata
psn-unified.js:873 [PSN] Browser tornato visibile - controllo sincronizzazione PSN
script.js:1167 [SOCKET] Ricevuto evento gameState: Object
script.js:1171 [SOCKET] Chiamando handleGameStateEvent
script.js:10799 [GAME STATE] Ricevuto stato con proprietà: Array(25)
script.js:10800 [GAME STATE] currentPlayerId RAW from server: SXcYhcjDerYA2bVjAAAX
script.js:10801 [GAME STATE] diceResult: Object
script.js:10802 [GAME STATE] initialPosition: f2
script.js:10803 [GAME STATE] mode: online
script.js:10804 [GAME STATE DEBUG] players: {"gbyArfSOUKYehImeAAAd":{"id":"gbyArfSOUKYehImeAAAd","color":"white","handSize":4,"score":0,"hand":[{"id":"Paper-3","suit":"Paper","value":"3"},{"id":"Paper-8","suit":"Paper","value":"8"},{"id":"Paper-9","suit":"Paper","value":"9"},{"id":"Paper-Q","suit":"Paper","value":"Q"}]},"SXcYhcjDerYA2bVjAAAX":{"id":"SXcYhcjDerYA2bVjAAAX","color":"black","handSize":5,"score":0,"hand":[{"id":"Rock-7","suit":"Rock","value":"7"},{"id":"Rock-Q","suit":"Rock","value":"Q"},{"id":"Rock-J","suit":"Rock","value":"J"},{"id":"Paper-2","suit":"Paper","value":"2"},{"id":"Rock-K","suit":"Rock","value":"K"}]}}
script.js:10805 [GAME STATE DEBUG] playerColors: {"gbyArfSOUKYehImeAAAd":"white","SXcYhcjDerYA2bVjAAAX":"black"}
script.js:10806 [GAME STATE DEBUG] playerNames: {"gbyArfSOUKYehImeAAAd":"bruscolino","SXcYhcjDerYA2bVjAAAX":"giggio"}
script.js:10807 [GAME STATE DEBUG] window.myPlayerId: SXcYhcjDerYA2bVjAAAX
script.js:10822 [VALIDATION] Controllo stato (36 board, 2 players)
script.js:10851 [ROLLBACK DEBUG] Controllo rollback rispetto allo stato precedente...
script.js:11115 [VALIDATION] ✅ Stato valido - nessuna carta duplicata rilevata
script.js:11140 [GAME STATE] Stato già elaborato, skip elaborazione duplicata
script.js:2220 [VISIBILITY] Richiesta stato aggiornato consolidata per setup interrotto
script.js:2232 [VISIBILITY] Flag setup interrotto resettato
script.js:2255 [VISIBILITY BUG FIX] Partita multiplayer rilevata - NON ripristino le mani per evitare carte duplicate
script.js:2256 [VISIBILITY BUG FIX] Il server invierà lo stato aggiornato automaticamente
script.js:2260 [VISIBILITY] Partita multiplayer - sincronizzazione gestita da richiesta consolidata
script.js:1167 [SOCKET] Ricevuto evento gameState: Object
script.js:1171 [SOCKET] Chiamando handleGameStateEvent
script.js:10799 [GAME STATE] Ricevuto stato con proprietà: Array(25)
script.js:10800 [GAME STATE] currentPlayerId RAW from server: SXcYhcjDerYA2bVjAAAX
script.js:10801 [GAME STATE] diceResult: Object
script.js:10802 [GAME STATE] initialPosition: f2
script.js:10803 [GAME STATE] mode: online
script.js:10804 [GAME STATE DEBUG] players: {"gbyArfSOUKYehImeAAAd":{"id":"gbyArfSOUKYehImeAAAd","color":"white","handSize":4,"score":0,"hand":[{"id":"Paper-3","suit":"Paper","value":"3"},{"id":"Paper-8","suit":"Paper","value":"8"},{"id":"Paper-9","suit":"Paper","value":"9"},{"id":"Paper-Q","suit":"Paper","value":"Q"}]},"SXcYhcjDerYA2bVjAAAX":{"id":"SXcYhcjDerYA2bVjAAAX","color":"black","handSize":5,"score":0,"hand":[{"id":"Rock-7","suit":"Rock","value":"7"},{"id":"Rock-Q","suit":"Rock","value":"Q"},{"id":"Rock-J","suit":"Rock","value":"J"},{"id":"Paper-2","suit":"Paper","value":"2"},{"id":"Rock-K","suit":"Rock","value":"K"}]}}
script.js:10805 [GAME STATE DEBUG] playerColors: {"gbyArfSOUKYehImeAAAd":"white","SXcYhcjDerYA2bVjAAAX":"black"}
script.js:10806 [GAME STATE DEBUG] playerNames: {"gbyArfSOUKYehImeAAAd":"bruscolino","SXcYhcjDerYA2bVjAAAX":"giggio"}
script.js:10807 [GAME STATE DEBUG] window.myPlayerId: SXcYhcjDerYA2bVjAAAX
script.js:10822 [VALIDATION] Controllo stato (36 board, 2 players)
script.js:10851 [ROLLBACK DEBUG] Controllo rollback rispetto allo stato precedente...
script.js:11115 [VALIDATION] ✅ Stato valido - nessuna carta duplicata rilevata
script.js:11140 [GAME STATE] Stato già elaborato, skip elaborazione duplicata
psn-unified.js:882 [PSN] Mosse presenti dopo ritorno visibilità - forza sincronizzazione
psn-unified.js:915 [PSN] Sistema PSN standard non disponibile - cattura stato corrente
psn-unified.js:1848 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
psn-unified.js:1848 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
script.js:2294 [VISIBILITY] Richiesta stato gioco aggiornato dopo ritorno da minimizzato
script.js:1167 [SOCKET] Ricevuto evento gameState: Object
script.js:1171 [SOCKET] Chiamando handleGameStateEvent
script.js:10799 [GAME STATE] Ricevuto stato con proprietà: Array(25)
script.js:10800 [GAME STATE] currentPlayerId RAW from server: SXcYhcjDerYA2bVjAAAX
script.js:10801 [GAME STATE] diceResult: Object
script.js:10802 [GAME STATE] initialPosition: f2
script.js:10803 [GAME STATE] mode: online
script.js:10804 [GAME STATE DEBUG] players: {"gbyArfSOUKYehImeAAAd":{"id":"gbyArfSOUKYehImeAAAd","color":"white","handSize":4,"score":0,"hand":[{"id":"Paper-3","suit":"Paper","value":"3"},{"id":"Paper-8","suit":"Paper","value":"8"},{"id":"Paper-9","suit":"Paper","value":"9"},{"id":"Paper-Q","suit":"Paper","value":"Q"}]},"SXcYhcjDerYA2bVjAAAX":{"id":"SXcYhcjDerYA2bVjAAAX","color":"black","handSize":5,"score":0,"hand":[{"id":"Rock-7","suit":"Rock","value":"7"},{"id":"Rock-Q","suit":"Rock","value":"Q"},{"id":"Rock-J","suit":"Rock","value":"J"},{"id":"Paper-2","suit":"Paper","value":"2"},{"id":"Rock-K","suit":"Rock","value":"K"}]}}
script.js:10805 [GAME STATE DEBUG] playerColors: {"gbyArfSOUKYehImeAAAd":"white","SXcYhcjDerYA2bVjAAAX":"black"}
script.js:10806 [GAME STATE DEBUG] playerNames: {"gbyArfSOUKYehImeAAAd":"bruscolino","SXcYhcjDerYA2bVjAAAX":"giggio"}
script.js:10807 [GAME STATE DEBUG] window.myPlayerId: SXcYhcjDerYA2bVjAAAX
script.js:10822 [VALIDATION] Controllo stato (36 board, 2 players)
script.js:10851 [ROLLBACK DEBUG] Controllo rollback rispetto allo stato precedente...
script.js:11115 [VALIDATION] ✅ Stato valido - nessuna carta duplicata rilevata
script.js:11140 [GAME STATE] Stato già elaborato, skip elaborazione duplicata
