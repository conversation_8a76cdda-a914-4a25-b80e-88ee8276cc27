 [GIOCA TAB] Modalità online - originalTurnPlayerId: TK-PLEEUe9HpQDIOAAAS window.myPlayerId: TK-PLEEUe9HpQDIOAAAS isMyTurn: true
 [ONLINE UI] Nomi basati sui colori: P1 (BIANCO)=giggio, P2 (NERO)=bruscolino
 [UI] Nomi predefiniti: P1=giggio, P2=bruscolino
 [UPDATE UI DEBUG 2] SECONDA ASSEGNAZIONE - PRIMA di currentGameState = state:
 [UPDATE UI DEBUG 2] - state.originalCurrentPlayerId: TK-PLEEUe9HpQDIOAAAS
 [UPDATE UI DEBUG 2] - previousOriginalCurrentPlayerId: TK-PLEEUe9HpQDIOAAAS
 [UPDATE UI DEBUG 2] - currentGameState.originalCurrentPlayerId prima: TK-PLEEUe9HpQDIOAAAS
 [UPDATE UI FINAL] originalCurrentPlayerId già presente nello stato: TK-PLEEUe9HpQDIOAAAS
 [UPDATE UI DEBUG 2] SECONDA ASSEGNAZIONE - DOPO aggiornamento:
 [UPDATE UI DEBUG 2] - currentGameState.originalCurrentPlayerId: TK-PLEEUe9HpQDIOAAAS
 [PERSISTENCE] Salvataggio stato partita...
 [PERSISTENCE] Stato salvato: Object
 [ONLINE UI] INIZIO modalità online - player1/player2 già mappati da online-game.js
 [ONLINE UI] Identificato: io sono PLAYER2 (nero)
 [ONLINE UI] PROTEZIONE TURNO ATTIVA durante setup - Uso ID protetto: TK-PLEEUe9HpQDIOAAAS
 [ONLINE UI] Debug turno DETTAGLIATO:
 [ONLINE UI] - state.originalCurrentPlayerId: TK-PLEEUe9HpQDIOAAAS tipo: string
 [ONLINE UI] - effectiveCurrentPlayerId (usato): TK-PLEEUe9HpQDIOAAAS
 [ONLINE UI] - window.myPlayerId: TK-PLEEUe9HpQDIOAAAS tipo: string
 [ONLINE UI] - Confronto diretto (===): true
 [ONLINE UI] - Confronto == : true
 [ONLINE UI] - effectiveCurrentPlayerId presente?: true
 [ONLINE UI] - window.myPlayerId presente?: true
 [ONLINE UI] - isMyTurn: true
 [ONLINE UI] - isPlayer1Local: false isPlayer1Turn: false
 [ONLINE UI] - isPlayer2Local: true isPlayer2Turn: true
 [NAMES PROTECTION] renderHand intercettato, cards: 4
 [RENDER HAND] Rendering 4 cards to player1-hand, isClickable: false
 [RENDER HAND] Cards: Array(4)
 [BRUSCOLINO RENDER DEBUG] Rendering mano player1-hand, isClickable: false
 [HAND DEBUG] player1-hand: isClickable=false, handLength=4, isOpponentHand=true
 [OPPONENT HAND] player1-hand: previousSize=null, currentSize=4, hasPlayedCard=false
 [CARD DRAG] NON trascinabile carta Scissors-9 in player1-hand - isClickable:false, suit:Scissors, isLastCard:false, isReplayingMove:false, isSetupAnimating:false
 [BRUSCOLINO CARD DEBUG] NON ABILITO drag per carta Scissors-9 in player1-hand - motivo: isClickable=false, suit=Scissors, isLastCard=false, isReplayingMove=false, isSetupAnimating=false
 [CARD DRAG] NON trascinabile carta Scissors-J in player1-hand - isClickable:false, suit:Scissors, isLastCard:false, isReplayingMove:false, isSetupAnimating:false
 [BRUSCOLINO CARD DEBUG] NON ABILITO drag per carta Scissors-J in player1-hand - motivo: isClickable=false, suit=Scissors, isLastCard=false, isReplayingMove=false, isSetupAnimating=false
 [CARD DRAG] NON trascinabile carta Rock-J in player1-hand - isClickable:false, suit:Rock, isLastCard:false, isReplayingMove:false, isSetupAnimating:false
 [BRUSCOLINO CARD DEBUG] NON ABILITO drag per carta Rock-J in player1-hand - motivo: isClickable=false, suit=Rock, isLastCard=false, isReplayingMove=false, isSetupAnimating=false
 [CARD DRAG] NON trascinabile carta Scissors-5 in player1-hand - isClickable:false, suit:Scissors, isLastCard:false, isReplayingMove:false, isSetupAnimating:false
 [BRUSCOLINO CARD DEBUG] NON ABILITO drag per carta Scissors-5 in player1-hand - motivo: isClickable=false, suit=Scissors, isLastCard=false, isReplayingMove=false, isSetupAnimating=false
 [NAMES PROTECTION] renderHand intercettato, cards: 5
 [RENDER HAND] Rendering 5 cards to player2-hand, isClickable: true
 [RENDER HAND] Cards: Array(5)
 [BRUSCOLINO RENDER DEBUG] Rendering mano player2-hand, isClickable: true
 [HAND DEBUG] player2-hand: isClickable=true, handLength=5, isOpponentHand=false
 [CARD DRAG] Rendendo trascinabile carta Rock-10 in player2-hand
 [CARD DRAG] Verificando listener su elemento: 
 [BRUSCOLINO CARD DEBUG] ABILITO drag per carta Rock-10 in player2-hand
 [CARD DRAG] Listener aggiunti. Draggable: true, Cursor: grab
 [CARD DRAG] Rendendo trascinabile carta Paper-8 in player2-hand
 [CARD DRAG] Verificando listener su elemento: 
 [BRUSCOLINO CARD DEBUG] ABILITO drag per carta Paper-8 in player2-hand
 [CARD DRAG] Listener aggiunti. Draggable: true, Cursor: grab
 [CARD DRAG] Rendendo trascinabile carta Paper-6 in player2-hand
 [CARD DRAG] Verificando listener su elemento: 
 [BRUSCOLINO CARD DEBUG] ABILITO drag per carta Paper-6 in player2-hand
 [CARD DRAG] Listener aggiunti. Draggable: true, Cursor: grab
 [CARD DRAG] Rendendo trascinabile carta Rock-4 in player2-hand
 [CARD DRAG] Verificando listener su elemento: 
 [BRUSCOLINO CARD DEBUG] ABILITO drag per carta Rock-4 in player2-hand
 [CARD DRAG] Listener aggiunti. Draggable: true, Cursor: grab
 [CARD DRAG] Rendendo trascinabile carta Paper-10 in player2-hand
 [CARD DRAG] Verificando listener su elemento: 
 [BRUSCOLINO CARD DEBUG] ABILITO drag per carta Paper-10 in player2-hand
 [CARD DRAG] Listener aggiunti. Draggable: true, Cursor: grab
 [DEBUG] updateGameUI BEFORE renderBoard - boardArea.offsetHeight: 1274
 [DEBUG] updateGameUI BEFORE renderBoard - gameBoardElement.offsetHeight: 1237
 [DEBUG] renderBoard START - gameBoardElement.innerHTML (inizio): <div class="cell" id="cell-a1" data-row="1" data-col="a" data-notation="a1" data-listeners-added="tr...
 [DEBUG] renderBoard - Animazioni completate e tabellone esistente, uso updateBoardCardsOnly
 [BOARD DATA ONLY] Aggiornamento carte tabellone
 [BOARD DATA ONLY] Parametri ricevuti: Object
 [BOARD DATA ONLY] Inizio ciclo aggiornamento carte per 36 posizioni
 [BOARD DATA ONLY] Processando carta in b4: Object
 [BOARD DATA ONLY] Creando nuova carta in b4 per: Object
 [BOARD DATA ONLY] Carta aggiunta al DOM in b4
 [BOARD DATA ONLY] Processando carta in b5: Object
 [BOARD DATA ONLY] Creando nuova carta in b5 per: Object
 [BOARD DATA ONLY] Carta aggiunta al DOM in b5
 [DEBUG] updateGameUI AFTER renderBoard
 [DEBUG] updateGameUI AFTER renderBoard - gameBoardElement.offsetHeight: 1237
 [DEBUG] updateGameUI AFTER renderBoard - boardArea.offsetHeight: 1274
 [DECK COUNTER DEBUG] updateGameUI con state.deckSize: 28 (tipo: number)
 [DECK COUNTER DEBUG] Chiamando renderDeck con validDeckSize: 28
 [DECK COUNTER DEBUG] renderDeck chiamato con remainingCards: 28
 [DECK COUNTER DEBUG] Valori: oldCounterValue=39, newCounterValue=28
 [DECK COUNTER DEBUG] Contatore aggiornato a: 28
 [RIBALTONE UI] Controllo messaggio ribaltone. state.ribaltoneMessage: non presente
 [RIBALTONE UI] Controllo messaggio ribaltone. state.continueForOneTurn: false
 [GIOCA TAB] PROTEZIONE TURNO ATTIVA - Uso ID protetto: TK-PLEEUe9HpQDIOAAAS
 [GIOCA TAB] Modalità online - originalTurnPlayerId: TK-PLEEUe9HpQDIOAAAS window.myPlayerId: TK-PLEEUe9HpQDIOAAAS isMyTurn: true
 [TURN TIMER] Chiamando updateGameStateWithTurnTimer per partita online
 [TURN TIMER DEBUG] === ANALISI CONTROLLO TURNO ===
 [TURN TIMER DEBUG] gameState.currentPlayerId: player2
 [TURN TIMER DEBUG] gameState.originalCurrentPlayerId: TK-PLEEUe9HpQDIOAAAS
 [TURN TIMER DEBUG] gameState.myPlayerId: v-raHjleMGzXlw3LAAAW
 [TURN TIMER DEBUG] window.myPlayerId: TK-PLEEUe9HpQDIOAAAS
 [TURN TIMER DEBUG] window.socket?.id: v-raHjleMGzXlw3LAAAW
 [TURN TIMER DEBUG] mySocketId: v-raHjleMGzXlw3LAAAW
 [TURN TIMER DEBUG] isMyTurn (CORRETTO): true
 [TURN TIMER DEBUG] === FINE ANALISI ===
 [TIMER] Avvio timer di turno: 59 secondi rimanenti
 [TIMER] Avviato timer totale per giocatore 2
 [GAMESTATE] Rating giocatore 2 aggiornato: 1000
 [PSN] Usando handSize dal server per white: 4 carte rimanenti
 [PSN CAPTURE] Modalità multiplayer - NON registro mossa (gestita dal server)
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [VISIBILITY] Stato di visibilità cambiato: visible
 [VISIBILITY] Notificato al server: browser tornato visibile
 [VISIBILITY] Pagina tornata visibile, controllo animazioni in sospeso
 [VISIBILITY] Eseguo pulizia cover cards bloccate...
 [VISIBILITY] Trovate 11 cover cards bloccate, le rimuovo
 [VISIBILITY] Cover card rimossa
 [VISIBILITY] Cover card rimossa
 [VISIBILITY] Cover card rimossa
 [VISIBILITY] Cover card rimossa
 [VISIBILITY] Cover card rimossa
 [VISIBILITY] Cover card rimossa
 [VISIBILITY] Cover card rimossa
 [VISIBILITY] Cover card rimossa
 [VISIBILITY] Cover card rimossa
 [VISIBILITY] Cover card rimossa
 [VISIBILITY] Cover card rimossa
 [VISIBILITY] Animazione carta iniziale già in corso in background
 [ANIM] Fine animazione movimento. Rimuovo cover e mostro carta reale.
 [ANIM] Utilizzo fallback setTimeout per animazione finale
 [SOCKET] Cambio di visibilità del client ll4pF0dEkYe4ujamAAAU: hidden  
 [SOCKET] Un altro client è minimizzato, pronto a gestire le sue animazioni se necessario
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [ANIM] Fine animateInitialCardPlacement con fallback.
 [ANIMATION DEBUG] Verifica condizioni per animazione nomi: Object
 [ANIMATION] Animando con G1 (Bianco): giggio vs G2 (Nero): bruscolino
 [ANIMATION] Convenzione standard: Bianco in alto, Nero in basso
 [PLAYER NAMES ANIMATION] ===== FUNZIONE CHIAMATA =====
 [PLAYER NAMES ANIMATION] Avvio animazione nomi giocatori DIRETTA
 [PLAYER NAMES ANIMATION] Nomi forniti: giggio vs bruscolino
 [PLAYER NAMES ANIMATION] Usando nomi forniti direttamente: giggio vs bruscolino
 [PLAYER NAMES ANIMATION] Nomi giocatori finali: giggio vs bruscolino
 [PLAYER NAMES ANIMATION] Game board trovato: true
 [PLAYER NAMES ANIMATION] Game board dimensioni: DOMRect
 [AVATAR DEBUG] Cercando avatar per giggio per posizione top
 [AVATAR DEBUG] Livello determinato per giggio: Principiante
 [AVATAR DEBUG] Tentativo di caricamento avatar da: http://localhost:3000/img/avatar/Principiante.webp
 [AVATAR DEBUG] Avatar trovato nella cache: http://localhost:3000/img/avatar/Principiante.webp
 [AVATAR DEBUG] Cercando avatar per bruscolino per posizione bottom
 [AVATAR DEBUG] Livello determinato per bruscolino: Principiante
 [AVATAR DEBUG] Tentativo di caricamento avatar da: http://localhost:3000/img/avatar/Principiante.webp
 [AVATAR DEBUG] Avatar trovato nella cache: http://localhost:3000/img/avatar/Principiante.webp
 [PLAYER NAMES ANIMATION] Animazione creata dinamicamente e aggiunta al DOM
 [PLAYER NAMES ANIMATION] Container aggiunto al game-board: dynamic-names-animation
 [PLAYER NAMES ANIMATION] Game-board children count: 41
 [AVATAR DEBUG] Avatar player1 caricato con successo
 [AVATAR DEBUG] Avatar player2 caricato con successo
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PLAYER NAMES ANIMATION] Terminando animazione
 [PLAYER NAMES ANIMATION] Avvio fade-in immediato del game container
 [PLAYER NAMES ANIMATION] Fade-in avviato immediatamente
 [PLAYER NAMES ANIMATION] Animazione completata e rimossa
 [PLAYER NAMES ANIMATION] Fade-in completato
 [PLAYER NAMES ANIMATION] isSetupAnimating resettato a false alla fine reale dell'animazione
 [PLAYER NAMES ANIMATION] nameAnimationCompleted impostato a true - animazione nomi completata
 [PLAYER NAMES ANIMATION] Forzando re-rendering delle carte dopo reset isSetupAnimating...
 [SETUP RE-RENDER] Forzando re-rendering delle carte dopo fine setup...
 [SETUP RE-RENDER] Player player1 (white): isLocal=false, isMyTurn=true, clickable=false
 [SETUP RE-RENDER] originalCurrentPlayerId: TK-PLEEUe9HpQDIOAAAS, myPlayerId: TK-PLEEUe9HpQDIOAAAS
 [SETUP RE-RENDER] myColor: black, playerColor: white
 [SETUP RE-RENDER] Re-rendering mano white (player1), clickable: false
 [SETUP RE-RENDER] Carte esistenti nel DOM: 4
  [ERROR] Errore JavaScript rilevato: 
(anonymous) @ multiplayer.js:1795
  [ERROR] Messaggio: Uncaught ReferenceError: handleCardDragStart is not defined
(anonymous) @ multiplayer.js:1796
  [ERROR] File: http://localhost:3000/script.js
(anonymous) @ multiplayer.js:1797
  [ERROR] Linea: 848
(anonymous) @ multiplayer.js:1798
  [ERROR] Stack: ReferenceError: handleCardDragStart is not defined
    at http://localhost:3000/script.js:848:66
    at NodeList.forEach (<anonymous>)
    at http://localhost:3000/script.js:827:27
    at Array.forEach (<anonymous>)
    at forceReRenderCardsAfterSetup (http://localhost:3000/script.js:784:43)
    at http://localhost:3000/script.js:9395:21
(anonymous) @ multiplayer.js:1799
script.js:848  Uncaught 
 [DRAG START] Inizio trascinamento carta Paper-10 del giocatore black
 [DRAG START] isGameRunning: true, gameOver: false, isSetupAnimating: false
 [DRAG ONLINE] Modalità online rilevata, uso logica specializzata
 [DRAG ONLINE] currentPlayerId originale: player2
 [DRAG ONLINE] originalCurrentPlayerId: TK-PLEEUe9HpQDIOAAAS
 [DRAG ONLINE] window.myPlayerId: TK-PLEEUe9HpQDIOAAAS
 [DRAG START] PROTEZIONE TURNO ATTIVA - Uso ID protetto: TK-PLEEUe9HpQDIOAAAS
 [DRAG ONLINE] È il mio turno? true
 [DRAG ONLINE] Il mio colore: black
 [DRAG ONLINE] Sono player2 (nero)
 [DEBUG] effectiveTurnPlayerId: player2
 [DEBUG] turnPlayer extracted: Object
 Drag Start: Object
 Card suit: Paper Card value: 10 Type of value: string
 Current player hand: Array(5)
 Full hand data: [{"id":"Rock-10","suit":"Rock","value":"10"},{"id":"Paper-8","suit":"Paper","value":"8"},{"id":"Paper-6","suit":"Paper","value":"6"},{"id":"Rock-4","suit":"Rock","value":"4"},{"id":"Paper-10","suit":"Paper","value":"10"}]
 Card data being sent: Object
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [CELL DROP LISTENER] Evento drop catturato su cella: cell-b3 Event target: cell-b3
 [DROP SUCCESS] Carta rilasciata su cella: cell-b3 Drop ID: 1750965629772_wp4mixuqf Timestamp: 2025-06-26T19:20:29.772Z
 [DROP PROTECTION] Setting isProcessingAction = true and processingDropId = 1750965629772_wp4mixuqf
 Dropped Card Data: Object
 [DROP ONLINE] Modalità online rilevata, uso logica specializzata
 [DROP ONLINE] currentPlayerId originale: player2
 [DROP ONLINE] originalCurrentPlayerId: TK-PLEEUe9HpQDIOAAAS
 [DROP ONLINE] window.myPlayerId: TK-PLEEUe9HpQDIOAAAS
 [DROP CELL] PROTEZIONE TURNO ATTIVA - Uso ID protetto: TK-PLEEUe9HpQDIOAAAS
 [DROP ONLINE] È il mio turno? true
 [DROP ONLINE] Il mio colore: black
 [DROP ONLINE] Sono player2 (nero)
 [DROP DEBUG] effectiveTurnPlayerId: player2
 [DROP DEBUG] turnPlayer extracted: Object
 [CLIENT VALIDATION] Validating placement of Paper-10 at b3
 [CLIENT VALIDATION] Adjacent positions to b3: Array(4)
 [CLIENT VALIDATION] Checking against adjacent card Rock-K at b4
 [CLIENT VALIDATION] OK: Paper-10 beats Rock-K at b4
 [CLIENT VALIDATION] Placement valid - all adjacent checks passed
 (black) tenta di piazzare [DRAG] 10 di Paper su b3
 [ACTION] Calling gameModeManager.placeCard, Drop ID: 1750965629772_wp4mixuqf
 [GAME MODE] Tentativo di piazzare carta: Object in posizione: b3
 [ONLINE GAME] Posizionamento carta: Object in b3
 [DROP] Carta temporanea aggiunta alla cella: b3
 [OPPONENT MARKER] Rimossi tutti i marker - il giocatore locale ha fatto una mossa
 [DRAG END] DropEffect: move
 [PSN] Usando handSize dal server per white: 4 carte rimanenti
 [PSN CAPTURE] Modalità multiplayer - NON registro mossa (gestita dal server)
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [SOCKET] Ricevuto evento gameState: Object
 [SOCKET] Chiamando handleGameStateEvent
 [GAME STATE] Ricevuto stato con proprietà: Array(26)
 [GAME STATE] currentPlayerId RAW from server: _ye4gxhazLXy2xHrAAAM
 [GAME STATE] diceResult: Object
 [GAME STATE] initialPosition: b5
 [GAME STATE] mode: online
 [GAME STATE DEBUG] players: {"TK-PLEEUe9HpQDIOAAAS":{"id":"TK-PLEEUe9HpQDIOAAAS","color":"black","handSize":4,"score":0,"hand":[{"id":"Rock-10","suit":"Rock","value":"10"},{"id":"Paper-8","suit":"Paper","value":"8"},{"id":"Paper-6","suit":"Paper","value":"6"},{"id":"Rock-4","suit":"Rock","value":"4"}]},"_ye4gxhazLXy2xHrAAAM":{"id":"_ye4gxhazLXy2xHrAAAM","color":"white","handSize":4,"score":0,"hand":[{"id":"Scissors-9","suit":"Scissors","value":"9"},{"id":"Scissors-J","suit":"Scissors","value":"J"},{"id":"Rock-J","suit":"Rock","value":"J"},{"id":"Scissors-5","suit":"Scissors","value":"5"}]}}
 [GAME STATE DEBUG] playerColors: {"TK-PLEEUe9HpQDIOAAAS":"black","_ye4gxhazLXy2xHrAAAM":"white"}
 [GAME STATE DEBUG] playerNames: {"TK-PLEEUe9HpQDIOAAAS":"bruscolino","_ye4gxhazLXy2xHrAAAM":"giggio"}
 [GAME STATE DEBUG] window.myPlayerId: TK-PLEEUe9HpQDIOAAAS
 [VALIDATION DEBUG] Inizio controllo duplicati + rollback - board: 36 posizioni, players: 2
 [VALIDATION DEBUG] 🕐 Timestamp stato: 2025-06-26T19:20:29.840Z
 [VALIDATION DEBUG] 📱 Client info: Object
 [VALIDATION DEBUG] Carta sul board: Paper-10 in b3
 [VALIDATION DEBUG] Carta sul board: Rock-K in b4
 [VALIDATION DEBUG] Carta sul board: Rock-Q in b5
 [VALIDATION DEBUG] Totale carte sul board: 3
 [VALIDATION DEBUG] IDs sul board: Array(3)
 [VALIDATION DEBUG] Totale carte nelle mani: 8
 [VALIDATION DEBUG] IDs nelle mani: Array(8)
 [ROLLBACK DEBUG] Controllo rollback rispetto allo stato precedente...
 [VALIDATION DEBUG] 🔍 STATO BOARDCARDIDS PRIMA CONTROLLO MANI:
 [VALIDATION DEBUG] 🔍 boardCardIds size: 3
 [VALIDATION DEBUG] 🔍 boardCardIds contents: Array(3)
 [VALIDATION DEBUG] 🔍 Cerco specificamente Rock-6: false
 [VALIDATION DEBUG] 🔍 Cerco specificamente Rock-K: true
 [VALIDATION DEBUG] 🔍 Cerco specificamente Paper-A: false
 [VALIDATION DEBUG] 🔍 Cerco specificamente Paper-K: false
 [VALIDATION DEBUG] Controllo mano TK-PLEEUe9HpQDIOAAAS (black): 4 carte
 [VALIDATION DEBUG] 🔍 PRIMA DI CONTROLLARE MANO - boardCardIds size: 3
 [VALIDATION DEBUG] Carta in mano: Rock-10 (pos 0)
 [VALIDATION DEBUG] Controllo duplicato per Rock-10:
 [VALIDATION DEBUG] - card.id tipo: string, valore: "Rock-10"
 [VALIDATION DEBUG] - boardCardIds.has("Rock-10"): false
 [VALIDATION DEBUG] - boardCardIds size: 3
 [VALIDATION DEBUG] - boardCardIds contents: Array(3)
 [VALIDATION DEBUG] - Controllo diretto con for loop: false
 [VALIDATION DEBUG] ✅ Carta Rock-10 non è duplicata
 [VALIDATION DEBUG] Carta in mano: Paper-8 (pos 1)
 [VALIDATION DEBUG] Controllo duplicato per Paper-8:
 [VALIDATION DEBUG] - card.id tipo: string, valore: "Paper-8"
 [VALIDATION DEBUG] - boardCardIds.has("Paper-8"): false
 [VALIDATION DEBUG] - boardCardIds size: 3
 [VALIDATION DEBUG] - boardCardIds contents: Array(3)
 [VALIDATION DEBUG] - Controllo diretto con for loop: false
 [VALIDATION DEBUG] ✅ Carta Paper-8 non è duplicata
 [VALIDATION DEBUG] Carta in mano: Paper-6 (pos 2)
 [VALIDATION DEBUG] Controllo duplicato per Paper-6:
 [VALIDATION DEBUG] - card.id tipo: string, valore: "Paper-6"
 [VALIDATION DEBUG] - boardCardIds.has("Paper-6"): false
 [VALIDATION DEBUG] - boardCardIds size: 3
 [VALIDATION DEBUG] - boardCardIds contents: Array(3)
 [VALIDATION DEBUG] - Controllo diretto con for loop: false
 [VALIDATION DEBUG] ✅ Carta Paper-6 non è duplicata
 [VALIDATION DEBUG] Carta in mano: Rock-4 (pos 3)
 [VALIDATION DEBUG] Controllo duplicato per Rock-4:
 [VALIDATION DEBUG] - card.id tipo: string, valore: "Rock-4"
 [VALIDATION DEBUG] - boardCardIds.has("Rock-4"): false
 [VALIDATION DEBUG] - boardCardIds size: 3
 [VALIDATION DEBUG] - boardCardIds contents: Array(3)
 [VALIDATION DEBUG] - Controllo diretto con for loop: false
 [VALIDATION DEBUG] ✅ Carta Rock-4 non è duplicata
 [VALIDATION DEBUG] Controllo mano _ye4gxhazLXy2xHrAAAM (white): 4 carte
 [VALIDATION DEBUG] 🔍 PRIMA DI CONTROLLARE MANO - boardCardIds size: 3
 [VALIDATION DEBUG] Carta in mano: Scissors-9 (pos 0)
 [VALIDATION DEBUG] Controllo duplicato per Scissors-9:
 [VALIDATION DEBUG] - card.id tipo: string, valore: "Scissors-9"
 [VALIDATION DEBUG] - boardCardIds.has("Scissors-9"): false
 [VALIDATION DEBUG] - boardCardIds size: 3
 [VALIDATION DEBUG] - boardCardIds contents: Array(3)
 [VALIDATION DEBUG] - Controllo diretto con for loop: false
 [VALIDATION DEBUG] ✅ Carta Scissors-9 non è duplicata
 [VALIDATION DEBUG] Carta in mano: Scissors-J (pos 1)
 [VALIDATION DEBUG] Controllo duplicato per Scissors-J:
 [VALIDATION DEBUG] - card.id tipo: string, valore: "Scissors-J"
 [VALIDATION DEBUG] - boardCardIds.has("Scissors-J"): false
 [VALIDATION DEBUG] - boardCardIds size: 3
 [VALIDATION DEBUG] - boardCardIds contents: Array(3)
 [VALIDATION DEBUG] - Controllo diretto con for loop: false
 [VALIDATION DEBUG] ✅ Carta Scissors-J non è duplicata
 [VALIDATION DEBUG] Carta in mano: Rock-J (pos 2)
 [VALIDATION DEBUG] Controllo duplicato per Rock-J:
 [VALIDATION DEBUG] - card.id tipo: string, valore: "Rock-J"
 [VALIDATION DEBUG] - boardCardIds.has("Rock-J"): false
 [VALIDATION DEBUG] - boardCardIds size: 3
 [VALIDATION DEBUG] - boardCardIds contents: Array(3)
 [VALIDATION DEBUG] - Controllo diretto con for loop: false
 [VALIDATION DEBUG] ✅ Carta Rock-J non è duplicata
 [VALIDATION DEBUG] Carta in mano: Scissors-5 (pos 3)
 [VALIDATION DEBUG] Controllo duplicato per Scissors-5:
 [VALIDATION DEBUG] - card.id tipo: string, valore: "Scissors-5"
 [VALIDATION DEBUG] - boardCardIds.has("Scissors-5"): false
 [VALIDATION DEBUG] - boardCardIds size: 3
 [VALIDATION DEBUG] - boardCardIds contents: Array(3)
 [VALIDATION DEBUG] - Controllo diretto con for loop: false
 [VALIDATION DEBUG] ✅ Carta Scissors-5 non è duplicata
 [VALIDATION DEBUG] Duplicati trovati: false, dettagli: 0
 [VALIDATION DEBUG] Rollback trovati: false, dettagli: 0
 [FALLBACK VALIDATION] Totale carte trovate: 11
 [FALLBACK VALIDATION] Carte uniche: 11
 [VALIDATION] ✅ Stato valido - nessuna carta duplicata rilevata
 [VALIDATION] Stato salvato per confronti futuri
 [ID SYNC] ID corretto confermato: TK-PLEEUe9HpQDIOAAAS per username: bruscolino
 [SYNC DEBUG] Received board state: {"a1":null,"b1":null,"c1":null,"d1":null,"e1":null,"f1":null,"a2":null,"b2":null,"c2":null,"d2":null,"e2":null,"f2":null,"a3":null,"b3":{"suit":"Paper","value":"10","id":"Paper-10","ownerColor":"black"},"c3":null,"d3":null,"e3":null,"f3":null,"a4":null,"b4":{"suit":"Rock","value":"K","id":"Rock-K","ownerColor":"white"},"c4":null,"d4":null,"e4":null,"f4":null,"a5":null,"b5":{"suit":"Rock","value":"Q","id":"Rock-Q","ownerColor":"neutral"},"c5":null,"d5":null,"e5":null,"f5":null,"a6":null,"b6":null,"c6":null,"d6":null,"e6":null,"f6":null}
 [SYNC DEBUG] Board keys: Array(36)
 [GAME STATE] Stato ricevuto mentre processingDropId = 1750965629772_wp4mixuqf
 [GAME STATE] Turno cambiato, reset dei flag di elaborazione
 [IMMEDIATE NAMES] Skip aggiornamento nomi - già presenti nomi permanenti validi
 [IMMEDIATE NAMES] Nomi permanenti attuali: {"TK-PLEEUe9HpQDIOAAAS":"bruscolino","_ye4gxhazLXy2xHrAAAM":"giggio"}
 [PLAYER AREAS] Nomi definitivamente impostati, NON li tocco MAI
 [TURN INDICATOR] Aggiornamento solo indicatore turno
 [TURN INDICATOR] currentPlayerId ricevuto: _ye4gxhazLXy2xHrAAAM
 [TURN INDICATOR] Fallback - whitePlayerId: _ye4gxhazLXy2xHrAAAM blackPlayerId: TK-PLEEUe9HpQDIOAAAS
 [TURN INDICATOR] ✅ ID originale bianco rilevato - è il turno del giocatore BIANCO
 [TURN INDICATOR] ✅ Classe current-player aggiunta a Player1 (BIANCO)
 [TURN INDICATOR] ✅ Classe current-turn aggiunta a player1-area per GLOW
 [TURN INDICATOR DEBUG] Player1 classes: player-name loaded current-player
 [TURN INDICATOR DEBUG] Player2 classes: player-name loaded
 [TURN INDICATOR DEBUG] Body ready-for-play: true
 [TURN INDICATOR] 🎯 INDICATORE ATTIVO su Player1: giggio
 [TURN INDICATOR] === FINE AGGIORNAMENTO ===
 [GAME STATE] Partita online - continuo con flusso completo per garantire drag and drop
 [GAME STATE] isFirstStateReceived: false
 [GAME STATE] shouldShowDiceAnimation: false
 [PSN DEBUG] Controllo registrazione PSN (immediata)...
 [PSN DEBUG] window.PSN esistente: true
 [PSN DEBUG] registerMoveFromServer funzione: function
 [PSN AUTHORIRATIVE] Registrazione mosse dal server (immediata)
 [PSN SERVER] === REGISTRAZIONE AUTORITATIVA DAL SERVER ===
 [PSN SERVER] Nuova carta rilevata: Paper-10 in b3 (owner: black)
 [PSN SERVER] Carte rimanenti per black (TK-PLEEUe9HpQDIOAAAS): 4
 [PSN SERVER] Registrando mossa autoritativa: Paper-10 in b3 (black, 4 carte rimanenti)
 [PSN DEBUG] Inizio registerMove: card={"suit":"Paper","value":"10","id":"Paper-10"}, position=b3 (mossa), color=black, gainedVertexControl=false, cardsRemaining=4, state.currentTurn (prima)=0, state.currentPlayer=white
 [PSN DEBUG RegisterMove Pre-Check] state.pendingVertexControl attuale: undefined
 [PSN DEBUG RegisterMove Pre-Check] state.pendingVertexControl è null o undefined.
 [PSN DEBUG] Dentro else (isWhite è false): state.currentTurn non modificato, resta 0
 [PSN DEBUG RegisterMove Pending] state.pendingVertexControl è vuoto o non definito al momento del check.
 [PSN DEBUG] Oggetto 'move' creato (dopo check pending): {"turnNumber":0,"isWhite":false,"card":{"suit":"Paper","value":"10","id":"Paper-10"},"position":"b3","gainedVertexControl":false,"cardsRemaining":4,"timestamp":"2025-06-26T19:20:29.842Z"}
 [PSN] Gioco iniziato - prima mossa registrata
 [PSN] Prima mossa rilevata, cambio a vista Notazioni.
 [PSN] Mossa registrata (con debug): Turno 0, Nero - Paper 10 su b3. Prossimo giocatore: white. StateTurn: 0
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Skip rilevazione pescate - registrazione dal server in corso
 [PSN SERVER] Registrazione completata: 1 nuove mosse elaborate
 [TURN PROTECTION] 🛡️  Setup appena completato - attivo protezione post-setup per 3 secondi
 [TURN PROTECTION] wasInSetupPhase: true → isInSetupPhase: false
 [TURN PROTECTION] postSetupProtectionUntil impostato a: 2025-06-26T19:20:32.844Z
 [TURN PROTECTION] updatedIsInPostSetupProtection: true
 [TURN PROTECTION] updatedNeedsProtection: true
 [DECK COUNTER DEBUG] gameState ricevuto con deckSize: 28
 === STATO TURNO RICEVUTO ===
 [TURN SYNC] currentPlayerId dal server: _ye4gxhazLXy2xHrAAAM
 [TURN SYNC] myPlayerId locale: TK-PLEEUe9HpQDIOAAAS
 [TURN SYNC] window.myPlayerId (per mani): TK-PLEEUe9HpQDIOAAAS
 [TURN SYNC] mySocketId (per socket): v-raHjleMGzXlw3LAAAW
 [TURN SYNC] effectiveMyPlayerId (per mani): TK-PLEEUe9HpQDIOAAAS
 [TURN SYNC] effectiveTurnPlayerId (per turni): TK-PLEEUe9HpQDIOAAAS
 [TURN SYNC] state.mode: online
 [TURN SYNC] È il mio turno? (CORRETTO): false
 [TURN SYNC] playerColors: Object
 [TURN SYNC] playerNames: Object
 [TURN DEBUG] === ANALISI DETTAGLIATA TURNO ONLINE ===
 [TURN DEBUG] currentPlayerId dal server: _ye4gxhazLXy2xHrAAAM
 [TURN DEBUG] mySocketId attuale: v-raHjleMGzXlw3LAAAW
 [TURN DEBUG] Confronto diretto: false
 [TURN DEBUG] Giocatori nello stato:
 [TURN DEBUG]   TK-PLEEUe9HpQDIOAAAS (black): è currentPlayer? false
 [TURN DEBUG]   _ye4gxhazLXy2xHrAAAM (white): è currentPlayer? true
 [TURN DEBUG] isMyTurn dal game mode manager: true
 [TURN DEBUG] === FINE ANALISI ===
 ==========================
 [ONLINE GAME] Aggiornamento stato di gioco
 [ONLINE GAME] currentPlayerId BEFORE mapping: _ye4gxhazLXy2xHrAAAM
 [ONLINE GAME MAPPING] === INIZIO MAPPING DEBUG ===
 [ONLINE GAME MAPPING] state.players keys: Array(2)
 [ONLINE GAME MAPPING] this.myPlayerId: TK-PLEEUe9HpQDIOAAAS
 [ONLINE GAME MAPPING] state.currentPlayerId: _ye4gxhazLXy2xHrAAAM
 [ONLINE GAME MAPPING] state.originalCurrentPlayerId (prima): undefined
 [ONLINE GAME MAPPING] === DEBUG MAPPING ===
 [ONLINE GAME MAPPING] this.myPlayerId: TK-PLEEUe9HpQDIOAAAS
 [ONLINE GAME MAPPING] this.opponentId: _ye4gxhazLXy2xHrAAAM
 [ONLINE GAME MAPPING] state.currentPlayerId: _ye4gxhazLXy2xHrAAAM
 [ONLINE GAME MAPPING] localPlayerData: Object
 [ONLINE GAME MAPPING] opponentPlayerData: Object
 [ONLINE GAME MAPPING] Io sono NERO, assegno: player1=opponent, player2=me
 [ONLINE GAME MAPPING] 🔍 Verifica mani dopo mapping:
 [ONLINE GAME MAPPING] 🔍 player1.hand: Array(4)
 [ONLINE GAME MAPPING] 🔍 player2.hand: Array(4)
 [ONLINE GAME MAPPING] PRIMA trasformazione currentPlayerId: _ye4gxhazLXy2xHrAAAM
 [ONLINE GAME MAPPING] Controllo: currentPlayerId === myPlayerId? false
 [ONLINE GAME MAPPING] Controllo: currentPlayerId === opponentId? true
 [ONLINE GAME MAPPING] È il turno dell'AVVERSARIO, suo colore: white -> assegno currentPlayerId = player1
 [ONLINE GAME MAPPING] DOPO trasformazione currentPlayerId: player1
 [ONLINE GAME] Player mapping:
 [ONLINE GAME] - Player 1 (white): _ye4gxhazLXy2xHrAAAM
 [ONLINE GAME] - Player 2 (black): TK-PLEEUe9HpQDIOAAAS
 [ONLINE GAME] - Current player ID transformed: player1
 [ONLINE GAME] - Original current player ID (socket): _ye4gxhazLXy2xHrAAAM
 [ONLINE GAME MAPPING] === FINE DEBUG MAPPING ===
 [ONLINE GAME] currentPlayerId AFTER mapping: player1
 [ONLINE GAME] originalCurrentPlayerId saved as: _ye4gxhazLXy2xHrAAAM
 [POST-MAPPING VALIDATION] Controllo carte duplicate dopo mapping online
 [POST-MAPPING VALIDATION] 📋 Board carta: Paper-10 in b3
 [POST-MAPPING VALIDATION] 📋 Board carta: Rock-K in b4
 [POST-MAPPING VALIDATION] 📋 Board carta: Rock-Q in b5
 [POST-MAPPING VALIDATION] 📋 Totale carte board: 3
 [POST-MAPPING VALIDATION] 📋 IDs board: Array(3)
 [POST-MAPPING VALIDATION] 📋 Cerco Rock-6 nel board: false
 [POST-MAPPING VALIDATION] 👤 Controllo mano player1: 4 carte
 [POST-MAPPING VALIDATION] 👤 Mano carta: Scissors-9 (pos 0)
 [POST-MAPPING VALIDATION] 👤 Mano carta: Scissors-J (pos 1)
 [POST-MAPPING VALIDATION] 👤 Mano carta: Rock-J (pos 2)
 [POST-MAPPING VALIDATION] 👤 Mano carta: Scissors-5 (pos 3)
 [POST-MAPPING VALIDATION] 👤 Controllo mano player2: 4 carte
 [POST-MAPPING VALIDATION] 👤 Mano carta: Rock-10 (pos 0)
 [POST-MAPPING VALIDATION] 👤 Mano carta: Paper-8 (pos 1)
 [POST-MAPPING VALIDATION] 👤 Mano carta: Paper-6 (pos 2)
 [POST-MAPPING VALIDATION] 👤 Mano carta: Rock-4 (pos 3)
 [POST-MAPPING VALIDATION] ✅ Nessun duplicato post-mapping rilevato
 [FINAL VALIDATION] Controllo finale forzato per carte duplicate...
 [FINAL VALIDATION] ✅ Nessuna correzione finale necessaria
 [GAME STATE] Stato mani dopo aggiornamento:
 [GAME STATE] player1 (white): Array(4)
 [GAME STATE] player2 (black): Array(4)
 [PLAYER AREAS] Nomi definitivamente impostati, NON li tocco MAI
 [TURN INDICATOR] Aggiornamento solo indicatore turno
 [TURN INDICATOR] currentPlayerId ricevuto: player1
 [TURN INDICATOR] ✅ ID mappato player1 rilevato - è il turno del giocatore BIANCO
 [TURN INDICATOR] ✅ Classe current-player aggiunta a Player1 (BIANCO)
 [TURN INDICATOR] ✅ Classe current-turn aggiunta a player1-area per GLOW
 [TURN INDICATOR DEBUG] Player1 classes: player-name loaded current-player
 [TURN INDICATOR DEBUG] Player2 classes: player-name loaded
 [TURN INDICATOR DEBUG] Body ready-for-play: true
 [TURN INDICATOR] 🎯 INDICATORE ATTIVO su Player1: giggio
 [TURN INDICATOR] === FINE AGGIORNAMENTO ===
 [PERMANENT NAMES DEBUG] Server playerNames: {"TK-PLEEUe9HpQDIOAAAS":"bruscolino","_ye4gxhazLXy2xHrAAAM":"giggio"}
 [PERMANENT NAMES DEBUG] Attuali permanentPlayerNames: {"TK-PLEEUe9HpQDIOAAAS":"bruscolino","_ye4gxhazLXy2xHrAAAM":"giggio"}
 [PERMANENT NAMES] Mantenuto nome esistente per TK-PLEEUe9HpQDIOAAAS: bruscolino
 [PERMANENT NAMES] Mantenuto nome esistente per _ye4gxhazLXy2xHrAAAM: giggio
 [PERMANENT NAMES] Nomi permanenti finali: {"TK-PLEEUe9HpQDIOAAAS":"bruscolino","_ye4gxhazLXy2xHrAAAM":"giggio"}
 [PERMANENT COLORS DEBUG] Server playerColors: {"TK-PLEEUe9HpQDIOAAAS":"black","_ye4gxhazLXy2xHrAAAM":"white"}
 [PERMANENT COLORS DEBUG] Attuali permanentPlayerColors: {"TK-PLEEUe9HpQDIOAAAS":"black","_ye4gxhazLXy2xHrAAAM":"white"}
 [PERMANENT COLORS] Confermato colore per TK-PLEEUe9HpQDIOAAAS: black
 [PERMANENT COLORS] Confermato colore per _ye4gxhazLXy2xHrAAAM: white
 [PERMANENT COLORS] Colori permanenti finali: {"TK-PLEEUe9HpQDIOAAAS":"black","_ye4gxhazLXy2xHrAAAM":"white"}
 [PSN SYNC] Aggiornamento handSize nel PSN dai dati del server
 [PSN API] Aggiornamento handSize dai dati del server
 [PSN API] Aggiornato handSize bianco: 4
 [PSN API] Aggiornato handSize nero: 4
 [GAME STATE DEBUG] Entrando nel ramo else if per updateGameUI - isOnline: true
 [GAME STATE DEBUG] Chiamando updateGameUI dal ramo else if
 [UPDATE UI DEBUG 1] PRIMA di currentGameState = state:
 [UPDATE UI DEBUG 1] - state.originalCurrentPlayerId: _ye4gxhazLXy2xHrAAAM
 [UPDATE UI DEBUG 1] - previousOriginalCurrentPlayerId: _ye4gxhazLXy2xHrAAAM
 [UPDATE UI] originalCurrentPlayerId già presente nello stato: _ye4gxhazLXy2xHrAAAM
 [UPDATE UI DEBUG 1] DOPO aggiornamento:
 [UPDATE UI DEBUG 1] - currentGameState.originalCurrentPlayerId: _ye4gxhazLXy2xHrAAAM
 [ADVANTAGE] Calcolo vantaggio con stato: {"vertex-a1":null,"vertex-f1":null,"vertex-a6":null,"vertex-f6":null}
 [ADVANTAGE] Componenti: Vertici (0-0), Carte (0-0), Adiacenze (0-0)
 [ADVANTAGE] Valore vantaggio calcolato: 0
 [ADVANTAGE] Percentuale finale: 50.0%
 [DEBUG] Vantaggio calcolato: 50%
 [HISTORY] Aggiunta mossa #2 alla cronologia
 Tentativo di forzare rendering rating...
 Ratings dopo init: Object
 Aggiornamento avatar player1: player1
 Aggiornamento avatar player2: player2
 [GIOCA TAB] PROTEZIONE TURNO ATTIVA - Uso ID protetto: TK-PLEEUe9HpQDIOAAAS
 [GIOCA TAB] Modalità online - originalTurnPlayerId: TK-PLEEUe9HpQDIOAAAS window.myPlayerId: TK-PLEEUe9HpQDIOAAAS isMyTurn: true
 [ONLINE UI] Nomi basati sui colori: P1 (BIANCO)=giggio, P2 (NERO)=bruscolino
 [UI] Nomi predefiniti: P1=giggio, P2=bruscolino
 [UPDATE UI DEBUG 2] SECONDA ASSEGNAZIONE - PRIMA di currentGameState = state:
 [UPDATE UI DEBUG 2] - state.originalCurrentPlayerId: _ye4gxhazLXy2xHrAAAM
 [UPDATE UI DEBUG 2] - previousOriginalCurrentPlayerId: _ye4gxhazLXy2xHrAAAM
 [UPDATE UI DEBUG 2] - currentGameState.originalCurrentPlayerId prima: _ye4gxhazLXy2xHrAAAM
 [UPDATE UI FINAL] originalCurrentPlayerId già presente nello stato: _ye4gxhazLXy2xHrAAAM
 [UPDATE UI DEBUG 2] SECONDA ASSEGNAZIONE - DOPO aggiornamento:
 [UPDATE UI DEBUG 2] - currentGameState.originalCurrentPlayerId: _ye4gxhazLXy2xHrAAAM
 [PERSISTENCE] Salvataggio stato partita...
 [PERSISTENCE] Stato salvato: Object
 [ONLINE UI] INIZIO modalità online - player1/player2 già mappati da online-game.js
 [ONLINE UI] Identificato: io sono PLAYER2 (nero)
 [ONLINE UI] PROTEZIONE TURNO ATTIVA durante setup - Uso ID protetto: TK-PLEEUe9HpQDIOAAAS
 [ONLINE UI] Debug turno DETTAGLIATO:
 [ONLINE UI] - state.originalCurrentPlayerId: _ye4gxhazLXy2xHrAAAM tipo: string
 [ONLINE UI] - effectiveCurrentPlayerId (usato): TK-PLEEUe9HpQDIOAAAS
 [ONLINE UI] - window.myPlayerId: TK-PLEEUe9HpQDIOAAAS tipo: string
 [ONLINE UI] - Confronto diretto (===): true
 [ONLINE UI] - Confronto == : true
 [ONLINE UI] - effectiveCurrentPlayerId presente?: true
 [ONLINE UI] - window.myPlayerId presente?: true
 [ONLINE UI] - isMyTurn: true
 [ONLINE UI] - isPlayer1Local: false isPlayer1Turn: false
 [ONLINE UI] - isPlayer2Local: true isPlayer2Turn: true
 [NAMES PROTECTION] renderHand intercettato, cards: 4
 [RENDER HAND] Rendering 4 cards to player1-hand, isClickable: false
 [RENDER HAND] Cards: Array(4)
 [BRUSCOLINO RENDER DEBUG] Rendering mano player1-hand, isClickable: false
 [HAND DEBUG] player1-hand: isClickable=false, handLength=4, isOpponentHand=true
 [OPPONENT HAND] player1-hand: previousSize=4, currentSize=4, hasPlayedCard=false
 [CARD DRAG] NON trascinabile carta Scissors-9 in player1-hand - isClickable:false, suit:Scissors, isLastCard:false, isReplayingMove:false, isSetupAnimating:false
 [BRUSCOLINO CARD DEBUG] NON ABILITO drag per carta Scissors-9 in player1-hand - motivo: isClickable=false, suit=Scissors, isLastCard=false, isReplayingMove=false, isSetupAnimating=false
 [CARD DRAG] NON trascinabile carta Scissors-J in player1-hand - isClickable:false, suit:Scissors, isLastCard:false, isReplayingMove:false, isSetupAnimating:false
 [BRUSCOLINO CARD DEBUG] NON ABILITO drag per carta Scissors-J in player1-hand - motivo: isClickable=false, suit=Scissors, isLastCard=false, isReplayingMove=false, isSetupAnimating=false
 [CARD DRAG] NON trascinabile carta Rock-J in player1-hand - isClickable:false, suit:Rock, isLastCard:false, isReplayingMove:false, isSetupAnimating:false
 [BRUSCOLINO CARD DEBUG] NON ABILITO drag per carta Rock-J in player1-hand - motivo: isClickable=false, suit=Rock, isLastCard=false, isReplayingMove=false, isSetupAnimating=false
 [CARD DRAG] NON trascinabile carta Scissors-5 in player1-hand - isClickable:false, suit:Scissors, isLastCard:false, isReplayingMove:false, isSetupAnimating:false
 [BRUSCOLINO CARD DEBUG] NON ABILITO drag per carta Scissors-5 in player1-hand - motivo: isClickable=false, suit=Scissors, isLastCard=false, isReplayingMove=false, isSetupAnimating=false
 [NAMES PROTECTION] renderHand intercettato, cards: 4
 [RENDER HAND] Rendering 4 cards to player2-hand, isClickable: true
 [RENDER HAND] Cards: Array(4)
 [BRUSCOLINO RENDER DEBUG] Rendering mano player2-hand, isClickable: true
 [HAND DEBUG] player2-hand: isClickable=true, handLength=4, isOpponentHand=false
 [CARD DRAG] Rendendo trascinabile carta Rock-10 in player2-hand
 [CARD DRAG] Verificando listener su elemento: 
 [BRUSCOLINO CARD DEBUG] ABILITO drag per carta Rock-10 in player2-hand
 [CARD DRAG] Listener aggiunti. Draggable: true, Cursor: grab
 [CARD DRAG] Rendendo trascinabile carta Paper-8 in player2-hand
 [CARD DRAG] Verificando listener su elemento: 
 [BRUSCOLINO CARD DEBUG] ABILITO drag per carta Paper-8 in player2-hand
 [CARD DRAG] Listener aggiunti. Draggable: true, Cursor: grab
 [CARD DRAG] Rendendo trascinabile carta Paper-6 in player2-hand
 [CARD DRAG] Verificando listener su elemento: 
 [BRUSCOLINO CARD DEBUG] ABILITO drag per carta Paper-6 in player2-hand
 [CARD DRAG] Listener aggiunti. Draggable: true, Cursor: grab
 [CARD DRAG] Rendendo trascinabile carta Rock-4 in player2-hand
 [CARD DRAG] Verificando listener su elemento: 
 [BRUSCOLINO CARD DEBUG] ABILITO drag per carta Rock-4 in player2-hand
 [CARD DRAG] Listener aggiunti. Draggable: true, Cursor: grab
 [DEBUG] updateGameUI BEFORE renderBoard - boardArea.offsetHeight: 1274
 [DEBUG] updateGameUI BEFORE renderBoard - gameBoardElement.offsetHeight: 1237
 [DEBUG] renderBoard START - gameBoardElement.innerHTML (inizio): <div class="cell" id="cell-a1" data-row="1" data-col="a" data-notation="a1" data-listeners-added="tr...
 [DEBUG] renderBoard - Animazioni completate e tabellone esistente, uso updateBoardCardsOnly
 [BOARD DATA ONLY] Aggiornamento carte tabellone
 [BOARD DATA ONLY] Parametri ricevuti: Object
 [BOARD DATA ONLY] Inizio ciclo aggiornamento carte per 36 posizioni
 [BOARD DATA ONLY] Processando carta in b3: Object
 [BOARD DATA ONLY] Rimuovendo carta temporanea in b3 per sostituirla con quella definitiva
 [BOARD DATA ONLY] Creando nuova carta in b3 per: Object
 [BOARD DATA ONLY] Carta aggiunta al DOM in b3
 [BOARD DATA ONLY] Processando carta in b4: Object
 [BOARD DATA ONLY] Processando carta in b5: Object
 [DEBUG] updateGameUI AFTER renderBoard
 [DEBUG] updateGameUI AFTER renderBoard - gameBoardElement.offsetHeight: 1237
 [DEBUG] updateGameUI AFTER renderBoard - boardArea.offsetHeight: 1274
 [DECK COUNTER DEBUG] updateGameUI con state.deckSize: 28 (tipo: number)
 [DECK COUNTER DEBUG] Chiamando renderDeck con validDeckSize: 28
 [DECK COUNTER DEBUG] renderDeck chiamato con remainingCards: 28
 [DECK COUNTER DEBUG] Valori: oldCounterValue=28, newCounterValue=28
 [RIBALTONE UI] Controllo messaggio ribaltone. state.ribaltoneMessage: non presente
 [RIBALTONE UI] Controllo messaggio ribaltone. state.continueForOneTurn: false
 [GIOCA TAB] PROTEZIONE TURNO ATTIVA - Uso ID protetto: TK-PLEEUe9HpQDIOAAAS
 [GIOCA TAB] Modalità online - originalTurnPlayerId: TK-PLEEUe9HpQDIOAAAS window.myPlayerId: TK-PLEEUe9HpQDIOAAAS isMyTurn: true
 [TURN TIMER] Chiamando updateGameStateWithTurnTimer per partita online
 [TURN TIMER DEBUG] === ANALISI CONTROLLO TURNO ===
 [TURN TIMER DEBUG] gameState.currentPlayerId: player1
 [TURN TIMER DEBUG] gameState.originalCurrentPlayerId: _ye4gxhazLXy2xHrAAAM
 [TURN TIMER DEBUG] gameState.myPlayerId: v-raHjleMGzXlw3LAAAW
 [TURN TIMER DEBUG] window.myPlayerId: TK-PLEEUe9HpQDIOAAAS
 [TURN TIMER DEBUG] window.socket?.id: v-raHjleMGzXlw3LAAAW
 [TURN TIMER DEBUG] mySocketId: v-raHjleMGzXlw3LAAAW
 [TURN TIMER DEBUG] isMyTurn (CORRETTO): false
 [TURN TIMER DEBUG] === FINE ANALISI ===
 [TIMER] Avviato timer totale per giocatore 1
 [GAMESTATE] Rating giocatore 2 aggiornato: 1000
 [PSN CAPTURE] ⚠️ Skip cattura - già in corso registrazione autoritativa dal server
 [PSN CAPTURE] ⚠️ Skip cattura - già in corso registrazione autoritativa dal server
 [PSN CAPTURE] ⚠️ Skip cattura - già in corso registrazione autoritativa dal server
 [PSN CAPTURE] ⚠️ Skip cattura - già in corso registrazione autoritativa dal server
 [PSN CAPTURE] ⚠️ Skip cattura - già in corso registrazione autoritativa dal server
 [PSN CAPTURE] ⚠️ Skip cattura - già in corso registrazione autoritativa dal server
 [PSN CAPTURE] ⚠️ Skip cattura - già in corso registrazione autoritativa dal server
 [PSN CAPTURE] ⚠️ Skip cattura - già in corso registrazione autoritativa dal server
 [PSN CAPTURE] ⚠️ Skip cattura - già in corso registrazione autoritativa dal server
 [PSN CAPTURE] ⚠️ Skip cattura - già in corso registrazione autoritativa dal server
 [PSN CAPTURE] ⚠️ Skip cattura - già in corso registrazione autoritativa dal server
 [PSN CAPTURE] ⚠️ Skip cattura - già in corso registrazione autoritativa dal server
 [PSN CAPTURE] ⚠️ Skip cattura - già in corso registrazione autoritativa dal server
 [PSN CAPTURE] ⚠️ Skip cattura - già in corso registrazione autoritativa dal server
 [PSN CAPTURE] ⚠️ Skip cattura - già in corso registrazione autoritativa dal server
 [PSN CAPTURE] ⚠️ Skip cattura - già in corso registrazione autoritativa dal server
 [PSN CAPTURE] ⚠️ Skip cattura - già in corso registrazione autoritativa dal server
 [PSN CAPTURE] ⚠️ Skip cattura - già in corso registrazione autoritativa dal server
 [PSN SERVER] Forzando aggiornamento finale della griglia PSN dopo registrazione dal server
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie
 [DROP PROTECTION] Auto-reset isProcessingAction = false and processingDropId = null after timeout
 [VISIBILITY] Stato di visibilità cambiato: hidden
 [VISIBILITY] Setup interrotto da minimizzazione: true
 [VISIBILITY] Notificato al server: browser minimizzato
 [VISIBILITY] Salvando lo stato originale delle mani dei giocatori
 [VISIBILITY] Pagina nascosta, preparo per riavvio animazioni
 [VISIBILITY] Stato di visibilità cambiato: visible
 [VISIBILITY] Notificato al server: browser tornato visibile
 [VISIBILITY] Setup era stato interrotto - applicando correzioni specifiche
 [VISIBILITY] Browser tornato visibile, ripristino stato originale delle mani
 [VISIBILITY] Pagina tornata visibile, controllo animazioni in sospeso
 [VISIBILITY] Eseguo pulizia cover cards bloccate...
 [VISIBILITY] Nessuna cover card bloccata trovata
 [VISIBILITY] NON riavvio animazione distribuzione carte, mostro stato attuale
 [SOCKET] Ricevuto evento gameState: Object
 [SOCKET] Chiamando handleGameStateEvent
 [GAME STATE] Ricevuto stato con proprietà: Array(25)
 [GAME STATE] currentPlayerId RAW from server: _ye4gxhazLXy2xHrAAAM
 [GAME STATE] diceResult: Object
 [GAME STATE] initialPosition: b5
 [GAME STATE] mode: online
 [GAME STATE DEBUG] players: {"TK-PLEEUe9HpQDIOAAAS":{"id":"TK-PLEEUe9HpQDIOAAAS","color":"black","handSize":4,"score":0,"hand":[{"id":"Rock-10","suit":"Rock","value":"10"},{"id":"Paper-8","suit":"Paper","value":"8"},{"id":"Paper-6","suit":"Paper","value":"6"},{"id":"Rock-4","suit":"Rock","value":"4"}]},"_ye4gxhazLXy2xHrAAAM":{"id":"_ye4gxhazLXy2xHrAAAM","color":"white","handSize":4,"score":0,"hand":[{"id":"Scissors-9","suit":"Scissors","value":"9"},{"id":"Scissors-J","suit":"Scissors","value":"J"},{"id":"Rock-J","suit":"Rock","value":"J"},{"id":"Scissors-5","suit":"Scissors","value":"5"}]}}
 [GAME STATE DEBUG] playerColors: {"TK-PLEEUe9HpQDIOAAAS":"black","_ye4gxhazLXy2xHrAAAM":"white"}
 [GAME STATE DEBUG] playerNames: {"TK-PLEEUe9HpQDIOAAAS":"bruscolino","_ye4gxhazLXy2xHrAAAM":"giggio"}
 [GAME STATE DEBUG] window.myPlayerId: TK-PLEEUe9HpQDIOAAAS
 [VALIDATION DEBUG] Inizio controllo duplicati + rollback - board: 36 posizioni, players: 2
 [VALIDATION DEBUG] 🕐 Timestamp stato: 2025-06-26T19:20:32.877Z
 [VALIDATION DEBUG] 📱 Client info: Object
 [VALIDATION DEBUG] Carta sul board: Paper-10 in b3
 [VALIDATION DEBUG] Carta sul board: Rock-K in b4
 [VALIDATION DEBUG] Carta sul board: Rock-Q in b5
 [VALIDATION DEBUG] Totale carte sul board: 3
 [VALIDATION DEBUG] IDs sul board: Array(3)
 [VALIDATION DEBUG] Totale carte nelle mani: 8
 [VALIDATION DEBUG] IDs nelle mani: Array(8)
 [ROLLBACK DEBUG] Controllo rollback rispetto allo stato precedente...
 [VALIDATION DEBUG] 🔍 STATO BOARDCARDIDS PRIMA CONTROLLO MANI:
 [VALIDATION DEBUG] 🔍 boardCardIds size: 3
 [VALIDATION DEBUG] 🔍 boardCardIds contents: Array(3)
 [VALIDATION DEBUG] 🔍 Cerco specificamente Rock-6: false
 [VALIDATION DEBUG] 🔍 Cerco specificamente Rock-K: true
 [VALIDATION DEBUG] 🔍 Cerco specificamente Paper-A: false
 [VALIDATION DEBUG] 🔍 Cerco specificamente Paper-K: false
 [VALIDATION DEBUG] Controllo mano TK-PLEEUe9HpQDIOAAAS (black): 4 carte
 [VALIDATION DEBUG] 🔍 PRIMA DI CONTROLLARE MANO - boardCardIds size: 3
 [VALIDATION DEBUG] Carta in mano: Rock-10 (pos 0)
 [VALIDATION DEBUG] Controllo duplicato per Rock-10:
 [VALIDATION DEBUG] - card.id tipo: string, valore: "Rock-10"
 [VALIDATION DEBUG] - boardCardIds.has("Rock-10"): false
 [VALIDATION DEBUG] - boardCardIds size: 3
 [VALIDATION DEBUG] - boardCardIds contents: Array(3)
 [VALIDATION DEBUG] - Controllo diretto con for loop: false
 [VALIDATION DEBUG] ✅ Carta Rock-10 non è duplicata
 [VALIDATION DEBUG] Carta in mano: Paper-8 (pos 1)
 [VALIDATION DEBUG] Controllo duplicato per Paper-8:
 [VALIDATION DEBUG] - card.id tipo: string, valore: "Paper-8"
 [VALIDATION DEBUG] - boardCardIds.has("Paper-8"): false
 [VALIDATION DEBUG] - boardCardIds size: 3
 [VALIDATION DEBUG] - boardCardIds contents: Array(3)
 [VALIDATION DEBUG] - Controllo diretto con for loop: false
 [VALIDATION DEBUG] ✅ Carta Paper-8 non è duplicata
 [VALIDATION DEBUG] Carta in mano: Paper-6 (pos 2)
 [VALIDATION DEBUG] Controllo duplicato per Paper-6:
 [VALIDATION DEBUG] - card.id tipo: string, valore: "Paper-6"
 [VALIDATION DEBUG] - boardCardIds.has("Paper-6"): false
 [VALIDATION DEBUG] - boardCardIds size: 3
 [VALIDATION DEBUG] - boardCardIds contents: Array(3)
 [VALIDATION DEBUG] - Controllo diretto con for loop: false
 [VALIDATION DEBUG] ✅ Carta Paper-6 non è duplicata
 [VALIDATION DEBUG] Carta in mano: Rock-4 (pos 3)
 [VALIDATION DEBUG] Controllo duplicato per Rock-4:
 [VALIDATION DEBUG] - card.id tipo: string, valore: "Rock-4"
 [VALIDATION DEBUG] - boardCardIds.has("Rock-4"): false
 [VALIDATION DEBUG] - boardCardIds size: 3
 [VALIDATION DEBUG] - boardCardIds contents: Array(3)
 [VALIDATION DEBUG] - Controllo diretto con for loop: false
 [VALIDATION DEBUG] ✅ Carta Rock-4 non è duplicata
 [VALIDATION DEBUG] Controllo mano _ye4gxhazLXy2xHrAAAM (white): 4 carte
 [VALIDATION DEBUG] 🔍 PRIMA DI CONTROLLARE MANO - boardCardIds size: 3
 [VALIDATION DEBUG] Carta in mano: Scissors-9 (pos 0)
 [VALIDATION DEBUG] Controllo duplicato per Scissors-9:
 [VALIDATION DEBUG] - card.id tipo: string, valore: "Scissors-9"
 [VALIDATION DEBUG] - boardCardIds.has("Scissors-9"): false
 [VALIDATION DEBUG] - boardCardIds size: 3
 [VALIDATION DEBUG] - boardCardIds contents: Array(3)
 [VALIDATION DEBUG] - Controllo diretto con for loop: false
 [VALIDATION DEBUG] ✅ Carta Scissors-9 non è duplicata
 [VALIDATION DEBUG] Carta in mano: Scissors-J (pos 1)
 [VALIDATION DEBUG] Controllo duplicato per Scissors-J:
 [VALIDATION DEBUG] - card.id tipo: string, valore: "Scissors-J"
 [VALIDATION DEBUG] - boardCardIds.has("Scissors-J"): false
 [VALIDATION DEBUG] - boardCardIds size: 3
 [VALIDATION DEBUG] - boardCardIds contents: Array(3)
 [VALIDATION DEBUG] - Controllo diretto con for loop: false
 [VALIDATION DEBUG] ✅ Carta Scissors-J non è duplicata
 [VALIDATION DEBUG] Carta in mano: Rock-J (pos 2)
 [VALIDATION DEBUG] Controllo duplicato per Rock-J:
 [VALIDATION DEBUG] - card.id tipo: string, valore: "Rock-J"
 [VALIDATION DEBUG] - boardCardIds.has("Rock-J"): false
script.js:10967 [VALIDATION DEBUG] - boardCardIds size: 3
script.js:10968 [VALIDATION DEBUG] - boardCardIds contents: Array(3)
script.js:10978 [VALIDATION DEBUG] - Controllo diretto con for loop: false
script.js:10996 [VALIDATION DEBUG] ✅ Carta Rock-J non è duplicata
script.js:10924 [VALIDATION DEBUG] Carta in mano: Scissors-5 (pos 3)
script.js:10964 [VALIDATION DEBUG] Controllo duplicato per Scissors-5:
script.js:10965 [VALIDATION DEBUG] - card.id tipo: string, valore: "Scissors-5"
script.js:10966 [VALIDATION DEBUG] - boardCardIds.has("Scissors-5"): false
script.js:10967 [VALIDATION DEBUG] - boardCardIds size: 3
script.js:10968 [VALIDATION DEBUG] - boardCardIds contents: Array(3)
script.js:10978 [VALIDATION DEBUG] - Controllo diretto con for loop: false
script.js:10996 [VALIDATION DEBUG] ✅ Carta Scissors-5 non è duplicata
script.js:11008 [VALIDATION DEBUG] Duplicati trovati: false, dettagli: 0
script.js:11009 [VALIDATION DEBUG] Rollback trovati: false, dettagli: 0
script.js:11062 [FALLBACK VALIDATION] Totale carte trovate: 11
script.js:11063 [FALLBACK VALIDATION] Carte uniche: 11
script.js:11194 [VALIDATION] ✅ Stato valido - nessuna carta duplicata rilevata
script.js:11203 [VALIDATION] Stato salvato per confronti futuri
script.js:11220 [GAME STATE] Stato già elaborato, skip elaborazione duplicata
script.js:2376 [VISIBILITY DEBUG] currentGameState presente: true
script.js:2377 [VISIBILITY DEBUG] renderGameState definito: true
script.js:2378 [VISIBILITY DEBUG] isMultiplayerGame: true
script.js:2380 [VISIBILITY DEBUG] currentGameState.mode: online
script.js:2387 [VISIBILITY] Partita multiplayer rilevata, skip renderGameState per evitare corruzione stato
[NEW] Explain Console errors by using Copilot in Edge: click
         
         to explain an error. 
        Learn more
        Don't show again
 [VISIBILITY] Richiesta stato aggiornato consolidata per setup interrotto
 [VISIBILITY] Flag setup interrotto resettato
 [VISIBILITY BUG FIX] Partita multiplayer rilevata - NON ripristino le mani per evitare carte duplicate
 [VISIBILITY BUG FIX] Il server invierà lo stato aggiornato automaticamente
 [VISIBILITY] Partita multiplayer - sincronizzazione gestita da richiesta consolidata
 [SOCKET] Ricevuto evento gameState: {gameId: 'Q96LJB', mode: 'online', players: {…}, board: {…}, discardPile: Array(0), …}
 [SOCKET] Chiamando handleGameStateEvent
 [GAME STATE] Ricevuto stato con proprietà: (25) ['gameId', 'mode', 'players', 'board', 'discardPile', 'currentPlayerId', 'diceResult', 'initialPosition', 'myPlayerId', 'myColor', 'playerColors', 'playerNames', 'gameOver', 'gameStarted', 'winner', 'winReason', 'deckSize', 'currentTurnIndex', 'vertexControl', 'loops', 'ratings', 'ribaltoneMessage', 'continueForOneTurn', 'errorMessage', 'initialCard']
 [GAME STATE] currentPlayerId RAW from server: _ye4gxhazLXy2xHrAAAM
 [GAME STATE] diceResult: {alpha: 'b', numeric: 5, color: 'black'}
 [GAME STATE] initialPosition: b5
 [GAME STATE] mode: online
 [GAME STATE DEBUG] players: {"TK-PLEEUe9HpQDIOAAAS":{"id":"TK-PLEEUe9HpQDIOAAAS","color":"black","handSize":4,"score":0,"hand":[{"id":"Rock-10","suit":"Rock","value":"10"},{"id":"Paper-8","suit":"Paper","value":"8"},{"id":"Paper-6","suit":"Paper","value":"6"},{"id":"Rock-4","suit":"Rock","value":"4"}]},"_ye4gxhazLXy2xHrAAAM":{"id":"_ye4gxhazLXy2xHrAAAM","color":"white","handSize":4,"score":0,"hand":[{"id":"Scissors-9","suit":"Scissors","value":"9"},{"id":"Scissors-J","suit":"Scissors","value":"J"},{"id":"Rock-J","suit":"Rock","value":"J"},{"id":"Scissors-5","suit":"Scissors","value":"5"}]}}
 [GAME STATE DEBUG] playerColors: {"TK-PLEEUe9HpQDIOAAAS":"black","_ye4gxhazLXy2xHrAAAM":"white"}
 [GAME STATE DEBUG] playerNames: {"TK-PLEEUe9HpQDIOAAAS":"bruscolino","_ye4gxhazLXy2xHrAAAM":"giggio"}
 [GAME STATE DEBUG] window.myPlayerId: TK-PLEEUe9HpQDIOAAAS
 [VALIDATION DEBUG] Inizio controllo duplicati + rollback - board: 36 posizioni, players: 2
 [VALIDATION DEBUG] 🕐 Timestamp stato: 2025-06-26T19:20:33.371Z
 [VALIDATION DEBUG] 📱 Client info: {myPlayerId: 'TK-PLEEUe9HpQDIOAAAS', socketId: 'v-raHjleMGzXlw3LAAAW', isVisible: true, isSetupAnimating: false, browserAgent: 'Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA'}
 [VALIDATION DEBUG] Carta sul board: Paper-10 in b3
 [VALIDATION DEBUG] Carta sul board: Rock-K in b4
 [VALIDATION DEBUG] Carta sul board: Rock-Q in b5
 [VALIDATION DEBUG] Totale carte sul board: 3
 [VALIDATION DEBUG] IDs sul board: (3) ['Paper-10', 'Rock-K', 'Rock-Q']
script.js:10853 [VALIDATION DEBUG] Totale carte nelle mani: 8
script.js:10854 [VALIDATION DEBUG] IDs nelle mani: (8) ['Rock-10', 'Paper-8', 'Paper-6', 'Rock-4', 'Scissors-9', 'Scissors-J', 'Rock-J', 'Scissors-5']
script.js:10862 [ROLLBACK DEBUG] Controllo rollback rispetto allo stato precedente...
script.js:10907 [VALIDATION DEBUG] 🔍 STATO BOARDCARDIDS PRIMA CONTROLLO MANI:
script.js:10908 [VALIDATION DEBUG] 🔍 boardCardIds size: 3
script.js:10909 [VALIDATION DEBUG] 🔍 boardCardIds contents: (3) ['Paper-10', 'Rock-K', 'Rock-Q']
script.js:10910 [VALIDATION DEBUG] 🔍 Cerco specificamente Rock-6: false
script.js:10911 [VALIDATION DEBUG] 🔍 Cerco specificamente Rock-K: true
script.js:10912 [VALIDATION DEBUG] 🔍 Cerco specificamente Paper-A: false
script.js:10913 [VALIDATION DEBUG] 🔍 Cerco specificamente Paper-K: false
script.js:10919 [VALIDATION DEBUG] Controllo mano TK-PLEEUe9HpQDIOAAAS (black): 4 carte
script.js:10920 [VALIDATION DEBUG] 🔍 PRIMA DI CONTROLLARE MANO - boardCardIds size: 3
script.js:10924 [VALIDATION DEBUG] Carta in mano: Rock-10 (pos 0)
script.js:10964 [VALIDATION DEBUG] Controllo duplicato per Rock-10:
script.js:10965 [VALIDATION DEBUG] - card.id tipo: string, valore: "Rock-10"
script.js:10966 [VALIDATION DEBUG] - boardCardIds.has("Rock-10"): false
script.js:10967 [VALIDATION DEBUG] - boardCardIds size: 3
script.js:10968 [VALIDATION DEBUG] - boardCardIds contents: (3) ['Paper-10', 'Rock-K', 'Rock-Q']
script.js:10978 [VALIDATION DEBUG] - Controllo diretto con for loop: false
script.js:10996 [VALIDATION DEBUG] ✅ Carta Rock-10 non è duplicata
script.js:10924 [VALIDATION DEBUG] Carta in mano: Paper-8 (pos 1)
script.js:10964 [VALIDATION DEBUG] Controllo duplicato per Paper-8:
script.js:10965 [VALIDATION DEBUG] - card.id tipo: string, valore: "Paper-8"
script.js:10966 [VALIDATION DEBUG] - boardCardIds.has("Paper-8"): false
script.js:10967 [VALIDATION DEBUG] - boardCardIds size: 3
script.js:10968 [VALIDATION DEBUG] - boardCardIds contents: (3) ['Paper-10', 'Rock-K', 'Rock-Q']
script.js:10978 [VALIDATION DEBUG] - Controllo diretto con for loop: false
script.js:10996 [VALIDATION DEBUG] ✅ Carta Paper-8 non è duplicata
script.js:10924 [VALIDATION DEBUG] Carta in mano: Paper-6 (pos 2)
script.js:10964 [VALIDATION DEBUG] Controllo duplicato per Paper-6:
script.js:10965 [VALIDATION DEBUG] - card.id tipo: string, valore: "Paper-6"
script.js:10966 [VALIDATION DEBUG] - boardCardIds.has("Paper-6"): false
script.js:10967 [VALIDATION DEBUG] - boardCardIds size: 3
script.js:10968 [VALIDATION DEBUG] - boardCardIds contents: (3) ['Paper-10', 'Rock-K', 'Rock-Q']
script.js:10978 [VALIDATION DEBUG] - Controllo diretto con for loop: false
script.js:10996 [VALIDATION DEBUG] ✅ Carta Paper-6 non è duplicata
script.js:10924 [VALIDATION DEBUG] Carta in mano: Rock-4 (pos 3)
script.js:10964 [VALIDATION DEBUG] Controllo duplicato per Rock-4:
script.js:10965 [VALIDATION DEBUG] - card.id tipo: string, valore: "Rock-4"
script.js:10966 [VALIDATION DEBUG] - boardCardIds.has("Rock-4"): false
script.js:10967 [VALIDATION DEBUG] - boardCardIds size: 3
script.js:10968 [VALIDATION DEBUG] - boardCardIds contents: (3) ['Paper-10', 'Rock-K', 'Rock-Q']
script.js:10978 [VALIDATION DEBUG] - Controllo diretto con for loop: false
script.js:10996 [VALIDATION DEBUG] ✅ Carta Rock-4 non è duplicata
script.js:10919 [VALIDATION DEBUG] Controllo mano _ye4gxhazLXy2xHrAAAM (white): 4 carte
script.js:10920 [VALIDATION DEBUG] 🔍 PRIMA DI CONTROLLARE MANO - boardCardIds size: 3
script.js:10924 [VALIDATION DEBUG] Carta in mano: Scissors-9 (pos 0)
script.js:10964 [VALIDATION DEBUG] Controllo duplicato per Scissors-9:
script.js:10965 [VALIDATION DEBUG] - card.id tipo: string, valore: "Scissors-9"
script.js:10966 [VALIDATION DEBUG] - boardCardIds.has("Scissors-9"): false
script.js:10967 [VALIDATION DEBUG] - boardCardIds size: 3
script.js:10968 [VALIDATION DEBUG] - boardCardIds contents: (3) ['Paper-10', 'Rock-K', 'Rock-Q']
script.js:10978 [VALIDATION DEBUG] - Controllo diretto con for loop: false
script.js:10996 [VALIDATION DEBUG] ✅ Carta Scissors-9 non è duplicata
script.js:10924 [VALIDATION DEBUG] Carta in mano: Scissors-J (pos 1)
script.js:10964 [VALIDATION DEBUG] Controllo duplicato per Scissors-J:
script.js:10965 [VALIDATION DEBUG] - card.id tipo: string, valore: "Scissors-J"
script.js:10966 [VALIDATION DEBUG] - boardCardIds.has("Scissors-J"): false
script.js:10967 [VALIDATION DEBUG] - boardCardIds size: 3
script.js:10968 [VALIDATION DEBUG] - boardCardIds contents: (3) ['Paper-10', 'Rock-K', 'Rock-Q']
script.js:10978 [VALIDATION DEBUG] - Controllo diretto con for loop: false
script.js:10996 [VALIDATION DEBUG] ✅ Carta Scissors-J non è duplicata
script.js:10924 [VALIDATION DEBUG] Carta in mano: Rock-J (pos 2)
script.js:10964 [VALIDATION DEBUG] Controllo duplicato per Rock-J:
script.js:10965 [VALIDATION DEBUG] - card.id tipo: string, valore: "Rock-J"
script.js:10966 [VALIDATION DEBUG] - boardCardIds.has("Rock-J"): false
script.js:10967 [VALIDATION DEBUG] - boardCardIds size: 3
script.js:10968 [VALIDATION DEBUG] - boardCardIds contents: (3) ['Paper-10', 'Rock-K', 'Rock-Q']
script.js:10978 [VALIDATION DEBUG] - Controllo diretto con for loop: false
script.js:10996 [VALIDATION DEBUG] ✅ Carta Rock-J non è duplicata
script.js:10924 [VALIDATION DEBUG] Carta in mano: Scissors-5 (pos 3)
script.js:10964 [VALIDATION DEBUG] Controllo duplicato per Scissors-5:
script.js:10965 [VALIDATION DEBUG] - card.id tipo: string, valore: "Scissors-5"
script.js:10966 [VALIDATION DEBUG] - boardCardIds.has("Scissors-5"): false
script.js:10967 [VALIDATION DEBUG] - boardCardIds size: 3
script.js:10968 [VALIDATION DEBUG] - boardCardIds contents: (3) ['Paper-10', 'Rock-K', 'Rock-Q']
script.js:10978 [VALIDATION DEBUG] - Controllo diretto con for loop: false
script.js:10996 [VALIDATION DEBUG] ✅ Carta Scissors-5 non è duplicata
script.js:11008 [VALIDATION DEBUG] Duplicati trovati: false, dettagli: 0
script.js:11009 [VALIDATION DEBUG] Rollback trovati: false, dettagli: 0
script.js:11062 [FALLBACK VALIDATION] Totale carte trovate: 11
script.js:11063 [FALLBACK VALIDATION] Carte uniche: 11
script.js:11194 [VALIDATION] ✅ Stato valido - nessuna carta duplicata rilevata
script.js:11203 [VALIDATION] Stato salvato per confronti futuri
script.js:11220 [GAME STATE] Stato già elaborato, skip elaborazione duplicata
script.js:2294 [VISIBILITY] Richiesta stato gioco aggiornato dopo ritorno da minimizzato
script.js:1167 [SOCKET] Ricevuto evento gameState: {gameId: 'Q96LJB', mode: 'online', players: {…}, board: {…}, discardPile: Array(0), …}
script.js:1171 [SOCKET] Chiamando handleGameStateEvent
script.js:10799 [GAME STATE] Ricevuto stato con proprietà: (25) ['gameId', 'mode', 'players', 'board', 'discardPile', 'currentPlayerId', 'diceResult', 'initialPosition', 'myPlayerId', 'myColor', 'playerColors', 'playerNames', 'gameOver', 'gameStarted', 'winner', 'winReason', 'deckSize', 'currentTurnIndex', 'vertexControl', 'loops', 'ratings', 'ribaltoneMessage', 'continueForOneTurn', 'errorMessage', 'initialCard']
script.js:10800 [GAME STATE] currentPlayerId RAW from server: _ye4gxhazLXy2xHrAAAM
script.js:10801 [GAME STATE] diceResult: {alpha: 'b', numeric: 5, color: 'black'}
script.js:10802 [GAME STATE] initialPosition: b5
script.js:10803 [GAME STATE] mode: online
script.js:10804 [GAME STATE DEBUG] players: {"TK-PLEEUe9HpQDIOAAAS":{"id":"TK-PLEEUe9HpQDIOAAAS","color":"black","handSize":4,"score":0,"hand":[{"id":"Rock-10","suit":"Rock","value":"10"},{"id":"Paper-8","suit":"Paper","value":"8"},{"id":"Paper-6","suit":"Paper","value":"6"},{"id":"Rock-4","suit":"Rock","value":"4"}]},"_ye4gxhazLXy2xHrAAAM":{"id":"_ye4gxhazLXy2xHrAAAM","color":"white","handSize":4,"score":0,"hand":[{"id":"Scissors-9","suit":"Scissors","value":"9"},{"id":"Scissors-J","suit":"Scissors","value":"J"},{"id":"Rock-J","suit":"Rock","value":"J"},{"id":"Scissors-5","suit":"Scissors","value":"5"}]}}
script.js:10805 [GAME STATE DEBUG] playerColors: {"TK-PLEEUe9HpQDIOAAAS":"black","_ye4gxhazLXy2xHrAAAM":"white"}
script.js:10806 [GAME STATE DEBUG] playerNames: {"TK-PLEEUe9HpQDIOAAAS":"bruscolino","_ye4gxhazLXy2xHrAAAM":"giggio"}
script.js:10807 [GAME STATE DEBUG] window.myPlayerId: TK-PLEEUe9HpQDIOAAAS
script.js:10821 [VALIDATION DEBUG] Inizio controllo duplicati + rollback - board: 36 posizioni, players: 2
script.js:10822 [VALIDATION DEBUG] 🕐 Timestamp stato: 2025-06-26T19:20:34.182Z
script.js:10823 [VALIDATION DEBUG] 📱 Client info: {myPlayerId: 'TK-PLEEUe9HpQDIOAAAS', socketId: 'v-raHjleMGzXlw3LAAAW', isVisible: true, isSetupAnimating: false, browserAgent: 'Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA'}
script.js:10836 [VALIDATION DEBUG] Carta sul board: Paper-10 in b3
script.js:10836 [VALIDATION DEBUG] Carta sul board: Rock-K in b4
script.js:10836 [VALIDATION DEBUG] Carta sul board: Rock-Q in b5
script.js:10851 [VALIDATION DEBUG] Totale carte sul board: 3
script.js:10852 [VALIDATION DEBUG] IDs sul board: (3) ['Paper-10', 'Rock-K', 'Rock-Q']
script.js:10853 [VALIDATION DEBUG] Totale carte nelle mani: 8
script.js:10854 [VALIDATION DEBUG] IDs nelle mani: (8) ['Rock-10', 'Paper-8', 'Paper-6', 'Rock-4', 'Scissors-9', 'Scissors-J', 'Rock-J', 'Scissors-5']
script.js:10862 [ROLLBACK DEBUG] Controllo rollback rispetto allo stato precedente...
script.js:10907 [VALIDATION DEBUG] 🔍 STATO BOARDCARDIDS PRIMA CONTROLLO MANI:
script.js:10908 [VALIDATION DEBUG] 🔍 boardCardIds size: 3
script.js:10909 [VALIDATION DEBUG] 🔍 boardCardIds contents: (3) ['Paper-10', 'Rock-K', 'Rock-Q']
script.js:10910 [VALIDATION DEBUG] 🔍 Cerco specificamente Rock-6: false
script.js:10911 [VALIDATION DEBUG] 🔍 Cerco specificamente Rock-K: true
script.js:10912 [VALIDATION DEBUG] 🔍 Cerco specificamente Paper-A: false
script.js:10913 [VALIDATION DEBUG] 🔍 Cerco specificamente Paper-K: false
script.js:10919 [VALIDATION DEBUG] Controllo mano TK-PLEEUe9HpQDIOAAAS (black): 4 carte
script.js:10920 [VALIDATION DEBUG] 🔍 PRIMA DI CONTROLLARE MANO - boardCardIds size: 3
script.js:10924 [VALIDATION DEBUG] Carta in mano: Rock-10 (pos 0)
script.js:10964 [VALIDATION DEBUG] Controllo duplicato per Rock-10:
script.js:10965 [VALIDATION DEBUG] - card.id tipo: string, valore: "Rock-10"
script.js:10966 [VALIDATION DEBUG] - boardCardIds.has("Rock-10"): false
script.js:10967 [VALIDATION DEBUG] - boardCardIds size: 3
script.js:10968 [VALIDATION DEBUG] - boardCardIds contents: (3) ['Paper-10', 'Rock-K', 'Rock-Q']
script.js:10978 [VALIDATION DEBUG] - Controllo diretto con for loop: false
script.js:10996 [VALIDATION DEBUG] ✅ Carta Rock-10 non è duplicata
script.js:10924 [VALIDATION DEBUG] Carta in mano: Paper-8 (pos 1)
script.js:10964 [VALIDATION DEBUG] Controllo duplicato per Paper-8:
script.js:10965 [VALIDATION DEBUG] - card.id tipo: string, valore: "Paper-8"
script.js:10966 [VALIDATION DEBUG] - boardCardIds.has("Paper-8"): false
script.js:10967 [VALIDATION DEBUG] - boardCardIds size: 3
script.js:10968 [VALIDATION DEBUG] - boardCardIds contents: (3) ['Paper-10', 'Rock-K', 'Rock-Q']
script.js:10978 [VALIDATION DEBUG] - Controllo diretto con for loop: false
script.js:10996 [VALIDATION DEBUG] ✅ Carta Paper-8 non è duplicata
script.js:10924 [VALIDATION DEBUG] Carta in mano: Paper-6 (pos 2)
script.js:10964 [VALIDATION DEBUG] Controllo duplicato per Paper-6:
script.js:10965 [VALIDATION DEBUG] - card.id tipo: string, valore: "Paper-6"
script.js:10966 [VALIDATION DEBUG] - boardCardIds.has("Paper-6"): false
script.js:10967 [VALIDATION DEBUG] - boardCardIds size: 3
script.js:10968 [VALIDATION DEBUG] - boardCardIds contents: (3) ['Paper-10', 'Rock-K', 'Rock-Q']
script.js:10978 [VALIDATION DEBUG] - Controllo diretto con for loop: false
script.js:10996 [VALIDATION DEBUG] ✅ Carta Paper-6 non è duplicata
script.js:10924 [VALIDATION DEBUG] Carta in mano: Rock-4 (pos 3)
script.js:10964 [VALIDATION DEBUG] Controllo duplicato per Rock-4:
script.js:10965 [VALIDATION DEBUG] - card.id tipo: string, valore: "Rock-4"
script.js:10966 [VALIDATION DEBUG] - boardCardIds.has("Rock-4"): false
script.js:10967 [VALIDATION DEBUG] - boardCardIds size: 3
script.js:10968 [VALIDATION DEBUG] - boardCardIds contents: (3) ['Paper-10', 'Rock-K', 'Rock-Q']
script.js:10978 [VALIDATION DEBUG] - Controllo diretto con for loop: false
script.js:10996 [VALIDATION DEBUG] ✅ Carta Rock-4 non è duplicata
script.js:10919 [VALIDATION DEBUG] Controllo mano _ye4gxhazLXy2xHrAAAM (white): 4 carte
script.js:10920 [VALIDATION DEBUG] 🔍 PRIMA DI CONTROLLARE MANO - boardCardIds size: 3
script.js:10924 [VALIDATION DEBUG] Carta in mano: Scissors-9 (pos 0)
script.js:10964 [VALIDATION DEBUG] Controllo duplicato per Scissors-9:
script.js:10965 [VALIDATION DEBUG] - card.id tipo: string, valore: "Scissors-9"
script.js:10966 [VALIDATION DEBUG] - boardCardIds.has("Scissors-9"): false
script.js:10967 [VALIDATION DEBUG] - boardCardIds size: 3
script.js:10968 [VALIDATION DEBUG] - boardCardIds contents: (3) ['Paper-10', 'Rock-K', 'Rock-Q']
script.js:10978 [VALIDATION DEBUG] - Controllo diretto con for loop: false
script.js:10996 [VALIDATION DEBUG] ✅ Carta Scissors-9 non è duplicata
script.js:10924 [VALIDATION DEBUG] Carta in mano: Scissors-J (pos 1)
script.js:10964 [VALIDATION DEBUG] Controllo duplicato per Scissors-J:
script.js:10965 [VALIDATION DEBUG] - card.id tipo: string, valore: "Scissors-J"
script.js:10966 [VALIDATION DEBUG] - boardCardIds.has("Scissors-J"): false
script.js:10967 [VALIDATION DEBUG] - boardCardIds size: 3
script.js:10968 [VALIDATION DEBUG] - boardCardIds contents: (3) ['Paper-10', 'Rock-K', 'Rock-Q']
script.js:10978 [VALIDATION DEBUG] - Controllo diretto con for loop: false
script.js:10996 [VALIDATION DEBUG] ✅ Carta Scissors-J non è duplicata
script.js:10924 [VALIDATION DEBUG] Carta in mano: Rock-J (pos 2)
script.js:10964 [VALIDATION DEBUG] Controllo duplicato per Rock-J:
script.js:10965 [VALIDATION DEBUG] - card.id tipo: string, valore: "Rock-J"
script.js:10966 [VALIDATION DEBUG] - boardCardIds.has("Rock-J"): false
script.js:10967 [VALIDATION DEBUG] - boardCardIds size: 3
script.js:10968 [VALIDATION DEBUG] - boardCardIds contents: (3) ['Paper-10', 'Rock-K', 'Rock-Q']
script.js:10978 [VALIDATION DEBUG] - Controllo diretto con for loop: false
script.js:10996 [VALIDATION DEBUG] ✅ Carta Rock-J non è duplicata
script.js:10924 [VALIDATION DEBUG] Carta in mano: Scissors-5 (pos 3)
script.js:10964 [VALIDATION DEBUG] Controllo duplicato per Scissors-5:
script.js:10965 [VALIDATION DEBUG] - card.id tipo: string, valore: "Scissors-5"
script.js:10966 [VALIDATION DEBUG] - boardCardIds.has("Scissors-5"): false
script.js:10967 [VALIDATION DEBUG] - boardCardIds size: 3
script.js:10968 [VALIDATION DEBUG] - boardCardIds contents: (3) ['Paper-10', 'Rock-K', 'Rock-Q']
script.js:10978 [VALIDATION DEBUG] - Controllo diretto con for loop: false
script.js:10996 [VALIDATION DEBUG] ✅ Carta Scissors-5 non è duplicata
script.js:11008 [VALIDATION DEBUG] Duplicati trovati: false, dettagli: 0
script.js:11009 [VALIDATION DEBUG] Rollback trovati: false, dettagli: 0
script.js:11062 [FALLBACK VALIDATION] Totale carte trovate: 11
script.js:11063 [FALLBACK VALIDATION] Carte uniche: 11
script.js:11194 [VALIDATION] ✅ Stato valido - nessuna carta duplicata rilevata
script.js:11203 [VALIDATION] Stato salvato per confronti futuri
script.js:11220 [GAME STATE] Stato già elaborato, skip elaborazione duplicata
