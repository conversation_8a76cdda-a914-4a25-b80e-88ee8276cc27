/**
 * PSN Unified - Sistema completo per Portable Skèmino Notation
 *
 * Questo file unifica tutte le funzionalità del sistema PSN:
 * - Parser e formattazione della notazione
 * - Cattura delle mosse dal gioco
 * - Visualizzazione della notazione
 * - Integrazione con lo stato di gioco
 * - Sistema di fallback per situazioni di errore
 *
 * Progettato per funzionare in modo indipendente e affidabile anche
 * quando i componenti del gioco hanno problemi di accesso allo stato.
 */

(function() {
    // Configurazione globale
    const config = {
        // Tempi per inizializzazione e aggiornamenti
        initDelay: 2000,              // Tempo di attesa per l'inizializzazione
        captureDelay: 500,            // Tempo di attesa dopo la cattura di un evento
        periodicCheckInterval: 5000,  // Intervallo per il controllo periodico

        // Selettori DOM
        psnAreaSelector: '#skemino-notation-area',
        boardSelector: '#game-board',
        player1HandSelector: '#player1-hand, .white-hand',
        player2HandSelector: '#player2-hand, .black-hand',

        // Grid config
        gridConfig: {
            columns: 2,                // Numero di colonne nella griglia
            maxItems: 100              // Massimo numero di notazioni da conservare
        },

        // Stili
        normalStyles: {
            background: 'linear-gradient(145deg, rgba(20, 40, 80, 0.8), rgba(10, 20, 40, 0.8))',
            border: '1px solid rgba(120, 180, 255, 0.4)',
            color: '#e0f0ff'
        },
        emergencyStyles: {
            background: 'linear-gradient(145deg, rgba(60, 20, 20, 0.8), rgba(40, 10, 10, 0.8))',
            border: '1px solid rgba(255, 100, 100, 0.4)',
            color: '#ffe0e0'
        }
    };

    // Stato del sistema PSN
    let state = {
        initialized: false,            // Se il sistema è stato inizializzato
        emergencyMode: false,          // Se siamo in modalità di emergenza
        boardState: {},                // Stato corrente del tabellone
        initialPosition: null,         // Posizione della carta iniziale
        moveHistory: [],               // Cronologia delle mosse
        currentTurn: 0,                // Turno corrente
        currentPlayer: 'white',        // Giocatore corrente
        handCards: { white: [], black: [] }, // Carte in mano
        psnContent: null,              // Contenuto PSN corrente
        gridEnabled: true,             // La griglia è sempre attivata
        showHandsOnly: true,          // MODIFICATO: default a true
        firstMoveMadeSwitchDone: false, // NUOVO FLAG: per gestire il cambio di tab una sola volta
        notationsHistory: [],          // Cronologia delle notazioni per la griglia
        components: {
            psnArea: null,             // Elemento area PSN
            contentEl: null,           // Elemento contenuto PSN
            headerEl: null,            // Elemento intestazione PSN
            gridContainer: null        // Contenitore griglia notazioni
        },
        observers: [],                 // Observer DOM
        gameStarted: false,            // Se il gioco è iniziato (prima mossa giocata)
        initialCardsDrawn: false,      // Se le carte iniziali sono state distribuite
        lastDrawnCard: null,           // Ultima carta pescata (per visualizzazione)
        lastCardDrawTime: null,        // Timestamp dell'ultima carta pescata
        previousHandSizes: {}          // Dimensioni mani precedenti per rilevare pescate avversario
    };

    // API pubblica
    const publicAPI = {
        // Inizializzazione manuale
        init: function() {
            initPSN();
            return "Sistema PSN inizializzato";
        },

        // Aggiorna la notazione
        update: function() {
            captureCurrentBoardState();
            return generateNotation();
        },

        // Forza la modalità emergenza
        enableEmergencyMode: function() {
            state.emergencyMode = true;
            updateUIMode();
            return "Modalità emergenza attivata";
        },

        // Disattiva la modalità emergenza
        disableEmergencyMode: function() {
            state.emergencyMode = false;
            updateUIMode();
            return "Modalità emergenza disattivata";
        },
        
        // Nuova API per aggiornare il conteggio delle carte dai dati del server
        updateHandSizes: function(gameState) {
            if (!gameState || !gameState.players) return;
            
            console.log('[PSN API] Aggiornamento handSize dai dati del server');
            
            // Aggiorna i conteggi delle carte per ogni giocatore
            for (const [playerId, playerData] of Object.entries(gameState.players)) {
                if (playerData.handSize !== undefined) {
                    // Salva il conteggio aggiornato per uso futuro
                    state.previousHandSizes[playerId] = playerData.handSize;
                    
                    // Aggiorna anche il conteggio locale se abbiamo il colore del giocatore
                    if (playerData.color === 'white') {
                        console.log(`[PSN API] Aggiornato handSize bianco: ${playerData.handSize}`);
                        // Non sovrascriviamo state.handCards qui perché è basato sul DOM
                    } else if (playerData.color === 'black') {
                        console.log(`[PSN API] Aggiornato handSize nero: ${playerData.handSize}`);
                    }
                }
            }
        },

        // NUOVA FUNZIONE: Registra mosse autorevolmente dal server
        registerMoveFromServer: function(gameState) {
            registerMoveFromServer(gameState);
        },

        // NUOVA FUNZIONE: Notifica cambio di controllo vertice
        notifyVertexControlChange: function(position, isWhite) {
            console.log(`[PSN API] notifyVertexControlChange chiamata per posizione ${position}, isWhite=${isWhite}`);

            // Cerca dall'ultima mossa registrata a ritroso (ultime 3 mosse)
            // per una CORRISPONDENZA ESATTA.
            if (state.moveHistory && state.moveHistory.length > 0) {
                const startIndex = Math.max(0, state.moveHistory.length - 3);
                for (let i = state.moveHistory.length - 1; i >= startIndex; i--) {
                    const move = state.moveHistory[i];
                    // Verifica se la mossa corrisponde al giocatore E ALLA POSIZIONE ESATTA DEL VERTICE (position)
                    if (move.isWhite === isWhite && move.position === position) {
                        console.log(`[PSN API] Trovata mossa ${move.card.suit}${move.card.value}@${move.position} corrispondente esatta in history per vertice ${position} (${isWhite ? 'bianco':'nero'}).`);
                        if (!move.gainedVertexControl) { // Applica solo se non già true, per evitare regenerate inutili
                            move.gainedVertexControl = true;
                            console.log(`[PSN API] ...impostato gainedVertexControl a true. Rigenero notazione.`);
                            generateNotation(); // Rigenera se abbiamo aggiornato una mossa esistente
                        } else {
                            console.log(`[PSN API] ...gainedVertexControl era già true. Nessuna azione.`);
                        }
                        // Se la mossa è stata trovata e aggiornata nella history,
                        // rimuoviamo eventuali pending control per la stessa combinazione di vertice e giocatore,
                        // per evitare che venga applicato due volte (es. da registerMove).
                        if (state.pendingVertexControl) {
                            const oldPendingLength = state.pendingVertexControl.length;
                            state.pendingVertexControl = state.pendingVertexControl.filter(vc =>
                                !(vc.isWhite === isWhite && vc.position === position)
                            );
                            if (state.pendingVertexControl.length < oldPendingLength) {
                                console.log(`[PSN API] Rimosso pending control per vertice ${position} (giocatore ${isWhite}) perché trovato e gestito in history.`);
                            }
                        }
                        return true; // Trovato e gestito
                    }
                }
            }

            // Se non è stata trovata e gestita una corrispondenza esatta nella cronologia:
            console.log(`[PSN API] Nessuna mossa ESATTA trovata in history per vertice ${position} e giocatore ${isWhite ? 'white' : 'black'}. Metto in pending.`);

            if (!state.pendingVertexControl) {
                state.pendingVertexControl = [];
            }

            // Evita di aggiungere duplicati a pendingVertexControl per la stessa posizione e giocatore
            const alreadyPending = state.pendingVertexControl.some(vc => vc.position === position && vc.isWhite === isWhite);
            if (!alreadyPending) {
                state.pendingVertexControl.push({
                    position: position, // Posizione del VERTICE che ha cambiato controllo
                    isWhite: isWhite,
                    timestamp: new Date()
                });
                console.log(`[PSN API] Memorizzata informazione di controllo vertice ${position} (${isWhite ? 'bianco' : 'nero'}) per applicazione futura:`,
                           state.pendingVertexControl[state.pendingVertexControl.length - 1]);
            } else {
                console.log(`[PSN API] Informazione di controllo vertice ${position} (${isWhite ? 'bianco' : 'nero'}) già in pending.`);
            }

            // NON chiamare generateNotation() qui se abbiamo solo messo in pending,
            // perché nessuna mossa *esistente* è stata modificata.
            // La notazione si aggiornerà quando registerMove userà il pendingVertexControl.
            return false; // Indica che è stato messo in pending o era già pending, e nessuna mossa esistente modificata.
        },

        // Alterna tra visualizzazione delle notazioni e delle mani
        toggleHandsView: function(showHands) {
            // Se è specificato un valore, lo usiamo, altrimenti invertiamo lo stato corrente
            state.showHandsOnly = showHands !== undefined ? showHands : !state.showHandsOnly;

            // Aggiorna il testo del pulsante se esiste
            const gridToggleBtn = document.querySelector('.psn-grid-toggle-btn');
            if (gridToggleBtn) {
                gridToggleBtn.innerHTML = state.showHandsOnly ?
                    '<i class="fas fa-th"></i> Mostra Notazioni' :
                    '<i class="fas fa-hand-paper"></i> Mostra Mani';
            }

            // Aggiorna la visualizzazione
            updateGridDisplay();

            return state.showHandsOnly ? "Visualizzazione mani attivata" : "Visualizzazione notazioni attivata";
        },

        // Reimposta tutti i dati
        reset: function() {
            state.boardState = {};
            state.initialPosition = null;
            state.moveHistory = [];
            state.notationsHistory = [];
            state.currentTurn = 0;
            state.currentPlayer = 'white';
            state.handCards = { white: [], black: [] };
            state.psnContent = null;
            state.gameStarted = false; // Reset del flag di gioco iniziato
            state.initialCardsDrawn = false; // Reset del flag di carte iniziali distribuite
            state.lastDrawnCard = null; // Reset ultima carta pescata
            state.lastCardDrawTime = null; // Reset timestamp pescata
            state.previousHandSizes = {}; // Reset dimensioni mani precedenti
        state.previousHandCards = {}; // Reset mani precedenti per confronto duplicati

            setTimeout(captureCurrentBoardState, 500);
            return "Dati PSN reimpostati";
        },

        // Ottieni la notazione corrente
        getNotation: function() {
            return state.psnContent || '';
        },

        // Ottieni lo stato attuale
        getState: function() {
            return {
                emergencyMode: state.emergencyMode,
                boardState: state.boardState,
                moveHistory: state.moveHistory,
                currentTurn: state.currentTurn,
                currentPlayer: state.currentPlayer,
                initialPosition: state.initialPosition,
                handCards: state.handCards,
                gridEnabled: state.gridEnabled,
                notationsHistory: state.notationsHistory
            };
        },

        // Visualizza informazioni diagnostiche
        debug: function() {
            return {
                status: {
                    initialized: state.initialized,
                    emergencyMode: state.emergencyMode,
                    standardComponentsAvailable: checkStandardPSNComponents(),
                    boardFound: !!document.querySelector(config.boardSelector),
                    movesCount: state.moveHistory.length,
                    cardsOnBoard: Object.keys(state.boardState).length,
                    gridEnabled: state.gridEnabled,
                    showHandsOnly: state.showHandsOnly,
                    notationsCount: state.notationsHistory.length
                },
                elements: {
                    psnArea: !!state.components.psnArea,
                    contentEl: !!state.components.contentEl,
                    headerEl: !!state.components.headerEl,
                    gridContainer: !!state.components.gridContainer
                },
                gameState: {
                    available: !!window.gameState,
                    boardAvailable: !!(window.gameState && window.gameState.board),
                    currentPlayer: window.gameState ? window.gameState.currentPlayerId : null
                }
            };
        }
    };

    // Inizializzazione del sistema
    setTimeout(initPSN, config.initDelay);

    // Funzione principale di inizializzazione
    function initPSN() {
        // Rimosso log: Inizializzazione del sistema PSN unificato

        if (state.initialized) {
            // Rimosso log: Sistema già inizializzato
            return;
        }

        // Verifica se il sistema PSN standard funziona
        checkStandardPSNComponents();

        // Crea/verifica l'area PSN
        ensurePSNArea();

        // Configura gli observer per monitorare il tabellone
        setupBoardObserver();

        // Configura i listener per eventi di gioco
        setupGameEventListeners();

        // Prima cattura dello stato del tabellone
        setTimeout(captureCurrentBoardState, 1000);

        // Aggiungi un listener per il pulsante di pesca carta
        const drawCardButton = document.getElementById('draw-card-button');
        if (drawCardButton) {
            drawCardButton.addEventListener('click', function(event) {
                // IMPORTANTE: Prima chiama la funzione di pescaggio carta dal gioco principale
                const isMultiplayerGame = window.currentGameState?.mode === 'online' || 
                                       window.myPlayerId || 
                                       window.permanentPlayerNames ||
                                       (window.socket && window.socket.connected);
                
                if (isMultiplayerGame) {
                    // CORREZIONE: Nelle partite multiplayer, verifica se è il tuo turno prima di bloccare
                    // PROTEZIONE TURNO: Rispetta la protezione del turno come le altre funzioni
                    let effectiveCurrentPlayerId = window.currentGameState?.originalCurrentPlayerId;
                    
                    if (window.initialTurnPlayerId && window.turnProtectionActive) {
                        console.log('[PSN] PROTEZIONE TURNO ATTIVA - Uso ID protetto:', window.initialTurnPlayerId);
                        effectiveCurrentPlayerId = window.initialTurnPlayerId;
                    } else {
                        console.log('[PSN] Protezione turno non attiva - Uso ID dal server:', effectiveCurrentPlayerId);
                    }
                    
                    const isMyTurn = effectiveCurrentPlayerId === window.myPlayerId;
                    
                    // DEBUG DETTAGLIATO per capire perché PSN fallisce
                    console.log('[PSN DEBUG] === ANALISI CONTROLLO TURNO PSN ===');
                    console.log('[PSN DEBUG] Timestamp PSN:', new Date().toISOString());
                    console.log('[PSN DEBUG] window.currentGameState:', window.currentGameState);
                    console.log('[PSN DEBUG] window.currentGameState?.originalCurrentPlayerId:', window.currentGameState?.originalCurrentPlayerId);
                    console.log('[PSN DEBUG] window.myPlayerId:', window.myPlayerId);
                    console.log('[PSN DEBUG] Confronto (===):', window.currentGameState?.originalCurrentPlayerId === window.myPlayerId);
                    console.log('[PSN DEBUG] isMyTurn (PSN):', isMyTurn);
                    console.log('[PSN DEBUG] AFTER SYNC CHECK - window.currentGameState === currentGameState (locale):', window.currentGameState === currentGameState);
                    
                    // DEBUG AGGIUNTIVO: Verifica se window.currentGameState ha altre proprietà
                    if (window.currentGameState) {
                        console.log('[PSN DEBUG] currentGameState.currentPlayerId:', window.currentGameState.currentPlayerId);
                        console.log('[PSN DEBUG] currentGameState.mode:', window.currentGameState.mode);
                        console.log('[PSN DEBUG] currentGameState.players:', window.currentGameState.players ? 'present' : 'missing');
                        console.log('[PSN DEBUG] currentGameState keys:', Object.keys(window.currentGameState));
                        console.log('[PSN DEBUG] currentGameState.originalCurrentPlayerId type:', typeof window.currentGameState.originalCurrentPlayerId);
                    } else {
                        console.log('[PSN DEBUG] window.currentGameState è null/undefined');
                    }
                    
                    console.log('[PSN DEBUG] === FINE ANALISI PSN ===');
                    
                    if (isMyTurn && typeof window.handleDrawCardClick === 'function') {
                        console.log('[PSN] Partita multiplayer - È il mio turno, permetto pesca carte');
                        window.handleDrawCardClick();
                    } else if (!isMyTurn) {
                        console.log('[PSN] Partita multiplayer - NON è il mio turno, blocco pesca carte');
                    } else {
                        console.warn('[PSN] handleDrawCardClick non disponibile in partita multiplayer');
                    }
                } else if (typeof window.handleDrawCardClick === 'function') {
                    console.log('[PSN] Chiamando handleDrawCardClick per pescare la carta');
                    window.handleDrawCardClick();
                } else {
                    console.warn('[PSN] handleDrawCardClick non disponibile, tentativo fallback');
                }

                // Attendiamo che la carta venga effettivamente pescata
                setTimeout(function() {
                    // Registra una notazione di pesca generica come fallback
                    try {
                        // Verifica se sono le carte iniziali
                        const isInitialHand = !state.initialCardsDrawn && state.moveHistory.length === 0;

                        // Non registriamo pescate per la mano iniziale
                        if (isInitialHand) {
                            state.initialCardsDrawn = true;
                            // Rimosso log: Clicco di pescata iniziale ignorato
                        }
                        // Solo se non è la mano iniziale, registriamo la pescata
                        else {
                            // Determina il giocatore corrente
                            const isWhite = state.currentPlayer === 'white';

                            // Rimosso log: Pulsante pesca carta: registrazione pescata

                            // Se siamo in modalità visualizzazione mani, passiamo alla modalità notazioni
                            // quando viene pescata una carta (per vedere la notazione della pescata)
                            if (state.showHandsOnly) {
                                console.log('[PSN] Pulsante pesca carta, cambio automatico a vista Notazioni.');
                                state.showHandsOnly = false;
                                
                                // Aggiorna il testo del pulsante toggle se esiste
                                const gridToggleBtn = document.querySelector('.psn-grid-toggle-btn');
                                if (gridToggleBtn) {
                                    gridToggleBtn.innerHTML = '<i class="fas fa-hand-paper"></i> Mostra Mani';
                                }
                                
                                // Aggiorna subito la griglia
                                updateGridDisplay();
                            }

                            // Non registriamo notazioni generiche per evitare duplicazioni
                            // Il sistema catturerà la carta specifica tramite l'evento cardDrawn
                        }
                    } catch (error) {
                        console.error('[PSN] Errore nel fallback pesca carta da pulsante:', error);
                    }

                    // Cattura standard dello stato
                    captureCurrentBoardState();
                }, 1000); // Attendiamo 1 secondo per dare tempo al sistema di elaborare la pesca
            });
        }

        // Configura il controllo periodico
        setInterval(periodicCheck, config.periodicCheckInterval);

        // Imposta flag di inizializzazione
        state.initialized = true;

        // Esponi l'API pubblica
        window.PSN = publicAPI;

        // Rimosso log: Sistema PSN unificato inizializzato con successo
    }

    // Verifica se i componenti standard del PSN sono disponibili e funzionanti
    function checkStandardPSNComponents() {
        // Rimosso log: Verifica componenti PSN standard

        // Verifica se sono disponibili i componenti PSN standard
        const hasPSNMoves = !!(window.psnMoves && typeof window.psnMoves.getPSNContent === 'function');
        const hasEnhancedPSN = !!(window.enhancedPSN && typeof window.enhancedPSN.getPSNContent === 'function');
        const hasVisualizer = !!(window.psnVisualizer && typeof window.psnVisualizer.loadPSNString === 'function');

        // Verifica se sono disponibili funzioni di aggiornamento
        const hasUpdateFunction = typeof window.updatePSNVisualizer === 'function';
        const hasDirectUpdate = typeof window.updatePSNFromBoard === 'function';

        // Verifica se c'è contenuto PSN
        let hasContent = false;

        if (hasPSNMoves) {
            try {
                const content = window.psnMoves.getPSNContent();
                hasContent = !!(content && content.trim() && content !== '0.e3||:white;:black');
            } catch (e) {
                console.warn('[PSN] Errore accesso content via psnMoves:', e);
            }
        } else if (hasEnhancedPSN) {
            try {
                const content = window.enhancedPSN.getPSNContent();
                hasContent = !!(content && content.trim() && content !== '0.e3||:white;:black');
            } catch (e) {
                console.warn('[PSN] Errore accesso content via enhancedPSN:', e);
            }
        }

        // Verifica lo stato di gioco
        const hasGameState = !!(window.gameState && window.gameState.board);

        const standardComponentsWorking = (hasPSNMoves || hasEnhancedPSN) &&
                                       (hasUpdateFunction || hasDirectUpdate) &&
                                       hasGameState;

        // Determina la modalità in base ai componenti disponibili
        if (!standardComponentsWorking || !hasContent) {
            if (!state.emergencyMode) {
                // Rimosso log: Sistemi standard non disponibili
                state.emergencyMode = true;
                updateUIMode();
            }
        } else if (state.emergencyMode && hasContent) {
            // Rimosso log: Sistemi standard ripristinati
            state.emergencyMode = false;
            updateUIMode();
        }

        // Rimosso log: Stato componenti standard

        return standardComponentsWorking && hasContent;
    }

    // Aggiorna la modalità dell'interfaccia utente
    function updateUIMode() {
        if (!state.components.psnArea) return;

        // Applica stili in base alla modalità
        if (state.emergencyMode) {
            // Aggiungi classe emergency-mode
            state.components.psnArea.classList.add('emergency-mode');

            // Aggiorna gli stili dinamici
            state.components.psnArea.style.background = config.emergencyStyles.background;
            state.components.psnArea.style.border = config.emergencyStyles.border;
            state.components.psnArea.style.color = config.emergencyStyles.color;

            // Aggiorna il contenuto con modalità emergenza
            if (state.components.headerEl) {
                state.components.headerEl.innerHTML = '<i class="fas fa-chess-board"></i> <span class="psn-header-title">Skèmino Notation</span>';
            }

            // Aggiorna il colore del contenuto
            if (state.components.contentEl) {
                state.components.contentEl.style.color = '#ffe0e0';
            }
        } else {
            // Rimuovi classe emergency-mode
            state.components.psnArea.classList.remove('emergency-mode');

            // Aggiorna gli stili dinamici
            state.components.psnArea.style.background = config.normalStyles.background;
            state.components.psnArea.style.border = config.normalStyles.border;
            state.components.psnArea.style.color = config.normalStyles.color;

            // Aggiorna il contenuto con modalità normale
            if (state.components.headerEl) {
                state.components.headerEl.innerHTML = '<i class="fas fa-chess-board"></i> <span class="psn-header-title">Skèmino Notation</span>';
            }

            // Aggiorna il colore del contenuto
            if (state.components.contentEl) {
                state.components.contentEl.style.color = '#e0f0ff';
            }
        }
    }

    // Assicura che l'area PSN esista e sia configurata correttamente
    function ensurePSNArea() {
        // Rimosso log: Verifica area PSN

        // Cerca l'area PSN standard
        let psnArea = document.querySelector(config.psnAreaSelector);

        // Se non esiste, creala
        if (!psnArea) {
            // Rimosso log: Area PSN non trovata, creazione

            // Cerca un contenitore adatto
            const sidebarContent = document.getElementById('sidebar-content') ||
                                document.getElementById('tab-gioca') ||
                                document.querySelector('.tab-content.active');

            const gameContainer = document.getElementById('game-container');
            const container = sidebarContent || gameContainer || document.body;

            // Crea l'area PSN
            psnArea = document.createElement('div');
            psnArea.id = 'skemino-notation-area';
            container.appendChild(psnArea);

            // Rimosso log: Area PSN creata in
        }

        // Applica classi CSS all'area PSN
        if (state.gridEnabled) {
            psnArea.classList.add('with-grid');
        }

        // Applica classe di emergenza se necessario
        if (state.emergencyMode) {
            psnArea.classList.add('emergency-mode');
        } else {
            psnArea.classList.remove('emergency-mode');
        }

        // Imposta colore di sfondo e bordo dal config
        psnArea.style.background = state.emergencyMode ? config.emergencyStyles.background : config.normalStyles.background;
        psnArea.style.border = state.emergencyMode ? config.emergencyStyles.border : config.normalStyles.border;
        psnArea.style.color = state.emergencyMode ? config.emergencyStyles.color : config.normalStyles.color;

        // Salva il riferimento all'area PSN
        state.components.psnArea = psnArea;

        // Crea intestazione se non esiste
        let headerEl = psnArea.querySelector('.psn-header');
        if (!headerEl) {
            headerEl = document.createElement('div');
            headerEl.className = 'psn-header';

            headerEl.innerHTML = '<i class="fas fa-chess-board"></i> <span class="psn-header-title">Skèmino Notation</span>';

            psnArea.appendChild(headerEl);
            state.components.headerEl = headerEl;
        }

        // Crea area contenuto se non esiste
        let contentEl = psnArea.querySelector('.psn-content');
        if (!contentEl) {
            contentEl = document.createElement('div');
            // Reset completo e dichiara classe per lo styling
            contentEl.className = 'psn-content psn-content-extended';
            contentEl.id = 'psn-content';

            // Imposta il colore in base alla modalità
            contentEl.style.color = state.emergencyMode ? '#ffe0e0' : '#e0f0ff';

            contentEl.textContent = 'Caricamento notazione PSN...';
            psnArea.appendChild(contentEl);
            state.components.contentEl = contentEl;
        }

        // Aggiungiamo un pulsante per alternare tra la visualizzazione delle notazioni e delle mani
        let gridToggleBtn = psnArea.querySelector('.psn-grid-toggle-btn');
        if (!gridToggleBtn) {
            gridToggleBtn = document.createElement('button');
            gridToggleBtn.className = 'psn-grid-toggle-btn';
            gridToggleBtn.innerHTML = state.showHandsOnly ?
                '<i class="fas fa-th"></i> Mostra Notazioni' :
                '<i class="fas fa-hand-paper"></i> Mostra Mani';

            gridToggleBtn.addEventListener('click', function() {
                // Rimosso log: Cambio visualizzazione tra notazioni e mani
                state.showHandsOnly = !state.showHandsOnly;
                this.innerHTML = state.showHandsOnly ?
                    '<i class="fas fa-th"></i> Mostra Notazioni' :
                    '<i class="fas fa-hand-paper"></i> Mostra Mani';

                // Aggiorna la visualizzazione
                updateGridDisplay();
            });

            headerEl.appendChild(gridToggleBtn);
        }

        // Assicurati che il pulsante abbia il testo corretto
        const existingToggleBtn = psnArea.querySelector('.psn-grid-toggle-btn');
        if (existingToggleBtn) {
            existingToggleBtn.innerHTML = state.showHandsOnly ?
                '<i class="fas fa-th"></i> Mostra Notazioni' :
                '<i class="fas fa-hand-paper"></i> Mostra Mani';
        }

        // Crea contenitore per la griglia se non esiste e se la modalità griglia è attiva
        ensureGridContainer(psnArea);
    }

    // Crea il contenitore per la griglia
    function ensureGridContainer(psnArea) {
        // La griglia è sempre attiva, quindi creiamo sempre il contenitore
        let gridContainer = psnArea.querySelector('.psn-grid-container');

        if (!gridContainer) {
            // Rimosso log: Creazione del contenitore griglia

            // Crea contenitore per la griglia
            gridContainer = document.createElement('div');
            gridContainer.className = 'psn-grid-container';
            // Imposta le colonne dal config
            gridContainer.style.gridTemplateColumns = `repeat(${config.gridConfig.columns}, 1fr)`;

            // Aggiungi il contenitore all'area PSN
            psnArea.appendChild(gridContainer);
            state.components.gridContainer = gridContainer;

            // Rimosso log: Contenitore griglia creato
        }
    }

    // Configura l'observer per monitorare modifiche al tabellone
    function setupBoardObserver() {
        // Rimosso log: Configurazione observer per il tabellone

        // Cerca il tabellone di gioco
        const gameBoard = document.querySelector(config.boardSelector);

        if (!gameBoard) {
            console.warn('[PSN] Tabellone di gioco non trovato, attesa...');

            // Riprova tra 2 secondi
            setTimeout(setupBoardObserver, 2000);
            return;
        }

        // Configurazione dell'observer
        const observerConfig = {
            childList: true,
            subtree: true,
            attributes: true,
            characterData: false
        };

        // Crea un nuovo MutationObserver
        const observer = new MutationObserver(function(mutations) {
            let boardChanged = false;

            mutations.forEach(function(mutation) {
                // Ignora cambiamenti che provengono dalla griglia PSN per evitare loop infiniti
                if (mutation.target && mutation.target.closest && 
                    (mutation.target.closest('.psn-area') || 
                     mutation.target.closest('.psn-grid-container') ||
                     mutation.target.classList?.contains('psn-grid-item') ||
                     mutation.target.classList?.contains('psn-empty-message') ||
                     mutation.target.classList?.contains('psn-standard-display'))) {
                    return; // Skip questo mutation
                }

                // Verifica se c'è stata una modifica rilevante al tabellone di gioco
                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                    for (let i = 0; i < mutation.addedNodes.length; i++) {
                        const node = mutation.addedNodes[i];
                        // Ignora nodi che sono parte della griglia PSN
                        if (node.classList && 
                            (node.classList.contains('psn-grid-item') ||
                             node.classList.contains('psn-empty-message') ||
                             node.classList.contains('psn-standard-display'))) {
                            continue;
                        }
                        if (node.classList &&
                            (node.classList.contains('card') ||
                            node.classList.contains('game-card'))) {
                            boardChanged = true;
                            break;
                        }
                    }
                } else if (mutation.type === 'attributes' &&
                          mutation.target.classList &&
                          !mutation.target.closest('.psn-area') && // Ignora attributi PSN
                          (mutation.target.classList.contains('cell') ||
                           mutation.target.classList.contains('card') ||
                           mutation.target.classList.contains('game-card'))) {
                    boardChanged = true;
                }
            });

            if (boardChanged) {
                // Rimosso log: Modifiche rilevate sul tabellone
                setTimeout(captureCurrentBoardState, config.captureDelay);
            }
        });

        // Avvia l'observer
        observer.observe(gameBoard, observerConfig);

        // Salva l'observer nella lista
        state.observers.push(observer);

        // Rimosso log: Observer configurato sul tabellone
    }

    // Configura i listener per gli eventi di gioco
    function setupGameEventListeners() {
        // Rimosso log: Configurazione listener per eventi di gioco

        // Evento: carta giocata
        document.addEventListener('cardPlayed', function(event) {
            // Rimosso log: Evento cardPlayed intercettato
            setTimeout(captureCurrentBoardState, config.captureDelay);
        });

        // Evento: carta pescata
        document.addEventListener('cardDrawn', function(event) {
            // Rimosso log: Evento cardDrawn intercettato

            // Registrazione diretta della pesca carta
            try {
                // Verifica se sono le carte iniziali
                const isInitialHand = !state.initialCardsDrawn && state.moveHistory.length === 0;

                // Non registriamo pescate per la mano iniziale
                if (isInitialHand) {
                    state.initialCardsDrawn = true;
                    // Rimosso log: Evento di pescata iniziale ignorato
                }
                // Altrimenti, procedi con la registrazione della pescata
                else if (event.detail && (event.detail.card || event.detail.playerId)) {
                    // Se siamo in modalità visualizzazione mani, passiamo alla modalità notazioni
                    // quando viene pescata una carta (per vedere la notazione della pescata)
                    if (state.showHandsOnly) {
                        console.log('[PSN] Evento cardDrawn, cambio automatico a vista Notazioni.');
                        state.showHandsOnly = false;
                        
                        // Aggiorna il testo del pulsante toggle se esiste
                        const gridToggleBtn = document.querySelector('.psn-grid-toggle-btn');
                        if (gridToggleBtn) {
                            gridToggleBtn.innerHTML = '<i class="fas fa-hand-paper"></i> Mostra Mani';
                        }
                    }
                    
                    console.log('[PSN DRAW DEBUG] === EVENTO CARD DRAWN RICEVUTO ===');
                    console.log('[PSN DRAW DEBUG] event.detail:', event.detail);
                    console.log('[PSN DRAW DEBUG] event.detail.card:', event.detail?.card);
                    console.log('[PSN DRAW DEBUG] event.detail.playerId:', event.detail?.playerId);
                    
                    // Determina il giocatore - usa event.detail.playerId se disponibile
                    let isWhite = state.currentPlayer === 'white';
                    
                    // Se abbiamo playerId nell'evento, usalo per determinare il giocatore
                    if (event.detail.playerId && window.permanentPlayerColors) {
                        const playerColor = window.permanentPlayerColors[event.detail.playerId];
                        if (playerColor) {
                            isWhite = playerColor === 'white';
                            console.log('[PSN DRAW DEBUG] Colore giocatore da event.detail.playerId:', playerColor, 'isWhite:', isWhite);
                        }
                    }

                    // Se abbiamo la carta nell'evento, usala
                    if (event.detail.card) {
                        console.log('[PSN DRAW DEBUG] Carta trovata nell\'evento');
                        const card = event.detail.card;
                        const cardSuit = card.suit || (typeof card.dataset === 'object' && card.dataset.cardSuit);
                        const cardValue = card.value || (typeof card.dataset === 'object' && card.dataset.cardValue);

                        if (cardSuit && cardValue) {
                            // Genera notazione della carta
                            const cardNotation = getCardNotation({suit: cardSuit, value: cardValue});
                            const handSize = isWhite ? state.handCards.white.length + 1 : state.handCards.black.length + 1;

                            console.log('[PSN DRAW DEBUG] Carta valida trovata:', cardNotation);

                            // Registra la pesca carta solo se abbiamo una notazione di carta valida
                            if (cardNotation && cardNotation !== 'Draw') {
                                registerCardDraw(cardNotation, isWhite, handSize);
                            }
                        }
                    } else {
                        // Se non abbiamo la carta nell'evento, usa una notazione generica
                        console.log('[PSN DRAW DEBUG] Carta non trovata nell\'evento, uso notazione generica');
                        
                        // Cattura le mani correnti per determinare la dimensione
                        captureHandCards();
                        const handSize = isWhite ? state.handCards.white.length : state.handCards.black.length;
                        
                        console.log('[PSN DRAW DEBUG] Hand size per giocatore:', handSize);
                        
                        // Usa una notazione generica "Draw" per la pesca
                        registerCardDraw('Draw', isWhite, handSize);
                    }
                }
            } catch (error) {
                console.error('[PSN] Errore nella registrazione diretta di pesca carta:', error);
            }

            // Cattura standard dello stato (come backup)
            setTimeout(captureCurrentBoardState, config.captureDelay);
        });

        // Evento: cambio di stato del gioco
        document.addEventListener('gameStateUpdated', function(event) {
            // Rimosso log: Evento gameStateUpdated intercettato
            setTimeout(captureCurrentBoardState, config.captureDelay);
        });

        // Evento: fine partita
        document.addEventListener('gameOver', function() {
            // Rimosso log: Evento gameOver intercettato
            setTimeout(captureCurrentBoardState, config.captureDelay);
        });

        // Evento: cambio turno
        document.addEventListener('turnChanged', function(event) {
            // Rimosso log: Evento turnChanged intercettato

            // Aggiorna il giocatore corrente se disponibile
            if (event.detail && event.detail.currentPlayer) {
                state.currentPlayer = event.detail.currentPlayer;
            }

            setTimeout(captureCurrentBoardState, config.captureDelay);
        });

        // ✅ NUOVO: Evento per gestire il ritorno di visibilità del browser
        document.addEventListener('visibilitychange', function() {
            if (document.visibilityState === 'visible') {
                console.log('[PSN] Browser tornato visibile - controllo sincronizzazione PSN');

                // Attendi un momento per permettere ad altri sistemi di sincronizzarsi
                setTimeout(() => {
                    // Forza controllo periodico immediato
                    periodicCheck();

                    // Se abbiamo mosse registrate ma la notazione non è visibile, forza aggiornamento
                    if (state.moveHistory.length > 0) {
                        console.log('[PSN] Mosse presenti dopo ritorno visibilità - forza sincronizzazione');

                        // Prova a ottenere contenuto dal sistema PSN standard
                        if (checkStandardPSNComponents()) {
                            let standardContent = '';

                            if (window.psnMoves && typeof window.psnMoves.getPSNContent === 'function') {
                                standardContent = window.psnMoves.getPSNContent();
                            } else if (window.enhancedPSN && typeof window.enhancedPSN.getPSNContent === 'function') {
                                standardContent = window.enhancedPSN.getPSNContent();
                            }

                            if (standardContent && standardContent.trim() && standardContent !== '0.e3||:white;:black') {
                                console.log('[PSN] Sincronizzazione contenuto PSN dopo ritorno visibilità');
                                state.psnContent = standardContent;
                                updateNotationDisplay(standardContent);

                                // Se è la prima mossa e non abbiamo ancora cambiato vista
                                if (state.moveHistory.length >= 1 && state.showHandsOnly && !state.firstMoveMadeSwitchDone) {
                                    console.log('[PSN] Prima mossa presente - cambio a vista Notazioni dopo ritorno visibilità');
                                    state.showHandsOnly = false;
                                    state.firstMoveMadeSwitchDone = true;

                                    const gridToggleBtn = document.querySelector('.psn-grid-toggle-btn');
                                    if (gridToggleBtn) {
                                        gridToggleBtn.innerHTML = '<i class="fas fa-hand-paper"></i> Mostra Mani';
                                    }

                                    updateGridDisplay();
                                }
                            }
                        } else {
                            // Fallback: cattura stato corrente
                            console.log('[PSN] Sistema PSN standard non disponibile - cattura stato corrente');
                            captureCurrentBoardState();
                        }
                    }
                }, 1000); // Attesa di 1 secondo per permettere sincronizzazione completa
            }
        });

        // Evento: inizio nuova partita
        document.addEventListener('gameStarted', function() {
            // Rimosso log: Evento gameStarted intercettato

            // Reset dei dati
            state.boardState = {};
            state.initialPosition = null;
            state.moveHistory = [];
            state.notationsHistory = []; // Reset della cronologia notazioni
            state.currentTurn = 0;
            state.currentPlayer = 'white';
            state.handCards = { white: [], black: [] };
            state.gameStarted = false; // Reset del flag di gioco iniziato
            state.initialCardsDrawn = false; // Reset del flag di carte iniziali distribuite

            // Assicura che esista l'area PSN
            ensurePSNArea();

            // Non mostriamo la notazione automaticamente all'inizio della partita

            // Ricattura lo stato iniziale
            setTimeout(captureInitialBoardState, 2000);
        });

        // Pulsanti per nuova partita
        const newGameButtons = ['start-new-game-button', 'new-game-button',
                            'play-online-button', 'training-button', 'start-local-button'];

        newGameButtons.forEach(function(buttonId) {
            const button = document.getElementById(buttonId);
            if (button) {
                button.addEventListener('click', function() {
                    // Rimosso log: Pulsante nuova partita rilevato

                    setTimeout(function() { // Manteniamo il timeout se serve per altre logiche di reset
                        // Reset dei dati esistenti
                        state.boardState = {};
                        state.initialPosition = null;
                        state.moveHistory = [];
                        state.notationsHistory = []; // Reset della cronologia notazioni
                        state.currentTurn = 0;
                        state.currentPlayer = 'white';
                        state.handCards = { white: [], black: [] };
                        state.gameStarted = false; // Reset del flag di gioco iniziato
                        state.initialCardsDrawn = false; // Reset del flag di carte iniziali distribuite

                        // --- INIZIO MODIFICA ---
                        state.showHandsOnly = true;         // Assicura che la vista Mani sia attiva
                        state.firstMoveMadeSwitchDone = false; // Resetta il flag per il cambio tab
                        // --- FINE MODIFICA ---

                        // Assicura che esista l'area PSN, ma senza contenuto iniziale
                        ensurePSNArea();

                        // --- INIZIO MODIFICA ---
                        // Aggiorna esplicitamente il testo del bottone e la griglia
                        const gridToggleBtn = document.querySelector('.psn-grid-toggle-btn');
                        if (gridToggleBtn) {
                            gridToggleBtn.innerHTML = state.showHandsOnly ?
                                '<i class="fas fa-th"></i> Mostra Notazioni' :
                                '<i class="fas fa-hand-paper"></i> Mostra Mani';
                        }
                        updateGridDisplay(); // Aggiorna la visualizzazione per mostrare le mani
                        // --- FINE MODIFICA ---

                        // Ricattura lo stato iniziale
                        setTimeout(captureInitialBoardState, 2000);
                    }, 1000); // Timeout esistente
                });
            }
        });

        // Monitor dei clic sul tabellone (fallback)
        document.addEventListener('click', function(event) {
            // Controlla se è stato cliccato un elemento del tabellone
            if (isTabelloneElement(event.target)) {
                // Attendi un po' per permettere alla UI di aggiornarsi
                setTimeout(captureCurrentBoardState, config.captureDelay);
            }
        });

        // Monitor dei drag-and-drop (fallback)
        document.addEventListener('dragend', function() {
            // Attendi un po' per permettere alla UI di aggiornarsi
            setTimeout(captureCurrentBoardState, config.captureDelay);
        });
    }

    // Verifica se un elemento fa parte del tabellone
    function isTabelloneElement(element) {
        if (!element || !element.classList) return false;

        // Controllo diretto delle classi
        if (element.classList.contains('cell') ||
            element.classList.contains('game-card') ||
            element.classList.contains('card')) {
            return true;
        }

        // Controllo ricorsivo sui genitori (fino a 3 livelli)
        let parent = element.parentElement;
        let depth = 0;

        while (parent && depth < 3) {
            if (parent.classList &&
                (parent.classList.contains('cell') ||
                parent.classList.contains('game-board'))) {
                return true;
            }
            parent = parent.parentElement;
            depth++;
        }

        return false;
    }

    // Cattura lo stato iniziale del tabellone
    function captureInitialBoardState() {
        // Rimosso log: Cattura dello stato iniziale del tabellone

        // Se il sistema standard funziona e non siamo in modalità emergenza, tentiamo di usarlo
        if (!state.emergencyMode && checkStandardPSNComponents()) {
            // Rimosso log: Utilizzo sistema standard per stato iniziale

            let standardContent = '';

            if (window.psnMoves && typeof window.psnMoves.getPSNContent === 'function') {
                standardContent = window.psnMoves.getPSNContent();
            } else if (window.enhancedPSN && typeof window.enhancedPSN.getPSNContent === 'function') {
                standardContent = window.enhancedPSN.getPSNContent();
            }

            if (standardContent && standardContent.trim()) {
                state.psnContent = standardContent;
                updateNotationDisplay(standardContent);
                return;
            }
        }

        // Altrimenti procediamo con la cattura diretta
        captureCurrentBoardState();
    }

    // Registra una notazione di pesca carta
    function registerCardDraw(cardNotationString, isWhiteCard, cardsRemaining) {
        
        try {
            if (!state.initialCardsDrawn && state.moveHistory.length === 0) {
                state.initialCardsDrawn = true;
                return false;
            }

            if (!state.gameStarted) {
                state.gameStarted = true;
            }

            // Logica per l'incremento del turno e cambio giocatore
            // Simile a registerMove
            if (isWhiteCard && state.currentPlayer === 'white') {
                const oldTurn = state.currentTurn;
                state.currentTurn++;
            } else if (isWhiteCard && state.currentPlayer !== 'white') {
                // Bianco pesca MA non è il suo turno
            } else { // Carta Nera
                // Nero pesca
            }

            // Se non abbiamo una notazione valida, non procedere
            if (!cardNotationString) {
                return false;
            }
            
            const effectiveTurnNumber = state.currentTurn;
            const notationToLog = cardNotationString;
            const remainingCardsCount = cardsRemaining || (isWhiteCard ? state.handCards.white.length : state.handCards.black.length);

            const drawNotationEntry = {
                turnNumber: effectiveTurnNumber,
                white: isWhiteCard ? `${notationToLog}|${remainingCardsCount}` : '', // Modificato da '...' a ''
                black: !isWhiteCard ? `${notationToLog}|${remainingCardsCount}` : '',
                timestamp: new Date(),
                isCardDraw: true
            };

            // Rimosso log: registerCardDraw: Oggetto 'drawNotationEntry' creato

            const existingIndex = state.notationsHistory.findIndex(n =>
                n.turnNumber === drawNotationEntry.turnNumber &&
                n.white === drawNotationEntry.white &&
                n.black === drawNotationEntry.black &&
                n.isCardDraw === true // Aggiungiamo questo per distinguere da mosse con stessa notazione (raro)
            );

            if (existingIndex === -1) {
                state.notationsHistory.push(drawNotationEntry);

                // Se siamo in modalità visualizzazione mani, passiamo alla modalità notazioni
                // quando viene pescata una carta (per vedere la notazione della pescata)
                if (state.showHandsOnly) {
                    state.showHandsOnly = false;
                    
                    // Aggiorna il testo del pulsante toggle se esiste
                    const gridToggleBtn = document.querySelector('.psn-grid-toggle-btn');
                    if (gridToggleBtn) {
                        gridToggleBtn.innerHTML = '<i class="fas fa-hand-paper"></i> Mostra Mani';
                    }
                }

                // Aggiorna subito la griglia
                updateGridDisplay();
            }

            // Cambia giocatore solo se l'azione è del giocatore corrente
            // e quindi fa avanzare il turno/passa la mano
            if ((isWhiteCard && state.currentPlayer === 'white') || (!isWhiteCard && state.currentPlayer === 'black')) {
                const oldPlayer = state.currentPlayer;
                state.currentPlayer = isWhiteCard ? 'black' : 'white';
            }
            return true;

        } catch (error) {
            console.error('[PSN] Errore nella registrazione della pesca carta:', error);
            return false;
        }
    }

    // Cattura lo stato attuale del tabellone
    function captureCurrentBoardState() {
        // **PROTEZIONE MULTIPLAYER**: Non catturare se stiamo già registrando dal server
        if (state.isRegisteringFromServer) {
            // THROTTLING: Log questo messaggio solo una volta ogni 5 secondi per evitare spam
            const now = Date.now();
            if (!state.lastSkipCaptureLogTime || (now - state.lastSkipCaptureLogTime) > 5000) {
                console.log('[PSN CAPTURE] ⚠️ Skip cattura - già in corso registrazione autoritativa dal server');
                state.lastSkipCaptureLogTime = now;
            }
            return;
        }
        
        // Rimosso log: Cattura dello stato attuale del tabellone

        // Se il sistema standard funziona e non siamo in modalità emergenza, tentiamo di usarlo
        if (!state.emergencyMode && checkStandardPSNComponents()) {
            // Rimosso log: Utilizzo sistema standard per lo stato attuale

            let standardContent = '';

            if (window.psnMoves && typeof window.psnMoves.getPSNContent === 'function') {
                standardContent = window.psnMoves.getPSNContent();
            } else if (window.enhancedPSN && typeof window.enhancedPSN.getPSNContent === 'function') {
                standardContent = window.enhancedPSN.getPSNContent();
            }

            if (standardContent && standardContent.trim() && standardContent !== '0.e3||:white;:black') {
                state.psnContent = standardContent;
                updateNotationDisplay(standardContent);
                return;
            }
        }

        try {
            // Cerca tutte le celle del tabellone con carte
            const cells = document.querySelectorAll('.cell');
            const newBoardState = {};
            let stateChanged = false;

            // Per ogni cella, controlla se contiene una carta
            cells.forEach(cell => {
                const position = cell.dataset.notation; // Notazione della cella (es. a1, b2, ecc.)
                if (!position) return;

                const cardElement = cell.querySelector('.game-card, .card');
                if (cardElement) {
                    // Estrai le informazioni della carta
                    const cardInfo = extractCardInfo(cardElement);

                    if (cardInfo) {
                        // Salva nel nuovo stato
                        newBoardState[position] = cardInfo;

                        // Verifica se questa è una carta nuova o modificata
                        if (!state.boardState[position] ||
                            state.boardState[position].suit !== cardInfo.suit ||
                            state.boardState[position].value !== cardInfo.value) {
                            // Rimosso log: Nuova carta rilevata in posizione
                            stateChanged = true;

                            // Se è la prima carta e non abbiamo una posizione iniziale, registrala
                            if (!state.initialPosition) {
                                                        // La carta iniziale di solito è neutra o al centro
                        const isNeutral = cardElement.classList.contains('neutral-card');
                        const isCenter = position === 'e3' || position === 'e2' || position === 'd3';

                        if (isNeutral || isCenter || Object.keys(state.boardState).length === 0) {
                            state.initialPosition = position;
                            // Rimosso log: Posizione iniziale identificata
                        }
                    }
                    // Altrimenti se non è la carta iniziale, registra come mossa
                    else if (position !== state.initialPosition) {
                        // --- RIMOZIONE BLOCCO SPECULATIVO ---
                        // La chiamata a registerMove ora DOVREBBE provenire da una fonte esterna autorevole.
                        
                        // IMPORTANTE: Ottieni il numero di carte rimanenti dal server
                        // invece di usare il conteggio locale
                        let cardsRemaining = undefined;
                        if (window.currentGameState && window.currentGameState.players) {
                            // Determina quale giocatore ha fatto la mossa basandosi sul colore della carta
                            const cardColor = getCardColor(cardElement);
                            
                            // Trova il giocatore corrispondente al colore
                            for (const [playerId, playerData] of Object.entries(window.currentGameState.players)) {
                                if (playerData.color === cardColor && playerData.handSize !== undefined) {
                                    cardsRemaining = playerData.handSize;
                                    console.log(`[PSN] Usando handSize dal server per ${cardColor}: ${cardsRemaining} carte rimanenti`);
                                    break;
                                }
                            }
                        }
                        
                        // Se non abbiamo il valore dal server, usa il conteggio memorizzato o locale come fallback
                        if (cardsRemaining === undefined) {
                            const cardColor = getCardColor(cardElement);
                            
                            // Prima prova con i conteggi memorizzati dal server
                            if (state.previousHandSizes) {
                                for (const [playerId, handSize] of Object.entries(state.previousHandSizes)) {
                                    if (window.permanentPlayerColors && window.permanentPlayerColors[playerId] === cardColor) {
                                        cardsRemaining = handSize;
                                        console.log(`[PSN] Usando handSize memorizzato per ${cardColor}: ${cardsRemaining} carte rimanenti`);
                                        break;
                                    }
                                }
                            }
                            
                            // Se ancora non abbiamo il valore, usa il conteggio locale
                            if (cardsRemaining === undefined) {
                                cardsRemaining = cardColor === 'white' ? 
                                    state.handCards.white.length : 
                                    state.handCards.black.length;
                                console.log(`[PSN] Fallback finale al conteggio locale per ${cardColor}: ${cardsRemaining} carte rimanenti`);
                            }
                        }
                                                        // ⚠️ DISABILITATO: captureCurrentBoardState NON dovrebbe chiamare registerMove
                        // in modalità multiplayer perché le mosse vengono registrate autorevolmente
                        // dal server tramite gli eventi gameAction e i dati corretti
                        // console.log('[PSN CAPTURE] SKIP registerMove - dovrebbe essere gestito dal server per multiplayer');
                        
                        // Solo per modalità locali dove non c'è server autoritativo
                        const isMultiplayerMode = window.currentGameState?.mode === 'online' || 
                                                window.myPlayerId || 
                                                window.permanentPlayerNames ||
                                                (window.socket && window.socket.connected);
                        
                        if (!isMultiplayerMode) {
                            console.log('[PSN CAPTURE] Modalità locale - registro mossa da captureCurrentBoardState');
                            registerMove(cardInfo, position, getCardColor(cardElement), false, cardsRemaining);
                        } else {
                            console.log('[PSN CAPTURE] Modalità multiplayer - NON registro mossa (gestita dal server)');
                        }
                            }
                        }
                    }
                }
            });

            // Aggiorna lo stato del tabellone
            state.boardState = newBoardState;

            // Cattura le carte in mano se possibile
            captureHandCards();

            // IMPORTANTE: Forziamo SEMPRE la generazione di notazione
            // Questo risolve i problemi di visualizzazione delle mosse
            // Rimosso log: Forzatura generazione notazione
            const notation = generateNotation();

            // Aggiorna anche la cronologia delle notazioni per la visualizzazione a griglia
            if (notation && state.gridEnabled) {
                updateNotationsHistory(notation);
            }

            // Controlla se l'elemento contentEl esiste
            if (state.components.contentEl) {
                // Verifica se il contenuto è visibile
                if (!state.components.contentEl.textContent || state.components.contentEl.textContent.trim() === '') {
                    // Rimosso log: Notazione scomparsa, riassegnazione forzata

                    // Forza la visualizzazione della notazione esistente o crea una notazione minima
                    if (state.psnContent) {
                        updateNotationDisplay(state.psnContent);
                    } else {
                        const setupPosition = state.initialPosition || 'e3';
                        const notation = `0.${setupPosition}||:white;:black\n`;
                        state.psnContent = notation;
                        updateNotationDisplay(notation);
                    }
                }

                // Verifica se la tabella è visibile e contiene elementi
                const tabella = state.components.contentEl.querySelector('table');
                if (!tabella) {
                    // Rimosso log: Tabella notazione mancante, riassegnazione forzata
                    updateNotationDisplay(state.psnContent || `0.${state.initialPosition || 'e3'}||:white;:black\n`);
                }
            }
        } catch (error) {
            console.error('[PSN] Errore durante la cattura dello stato del tabellone:', error);

            // Anche in caso di errori, tenta di visualizzare la notazione se disponibile
            if (state.psnContent && state.components.contentEl) {
                // Rimosso log: Tentativo di riassegnazione notazione dopo errore
                updateNotationDisplay(state.psnContent);
            }
        }
    }

    // Estrai le informazioni su una carta da un elemento del DOM
    function extractCardInfo(cardElement) {
        if (!cardElement) return null;

        try {
            // Estrai il tipo di carta
            let suit = null;
            let value = null;

            // Prova a determinare il seme e il valore dalle classi
            if (cardElement.classList) {
                const cardClasses = Array.from(cardElement.classList);

                // Cerca classi come "rock", "paper", "scissors" o "card-rock", ecc.
                if (cardElement.classList.contains('rock') || cardElement.classList.contains('card-rock')) {
                    suit = 'Rock';
                } else if (cardElement.classList.contains('paper') || cardElement.classList.contains('card-paper')) {
                    suit = 'Paper';
                } else if (cardElement.classList.contains('scissors') || cardElement.classList.contains('card-scissors')) {
                    suit = 'Scissors';
                }

                // Cerca classi per il valore
                const valueClass = cardClasses.find(cls => cls.startsWith('value-'));
                if (valueClass) {
                    value = valueClass.replace('value-', '');
                }
            }

            // Prova a determinare il seme e il valore dai data-attributes
            if ((!suit || !value) && cardElement.dataset) {
                if (cardElement.dataset.cardSuit) {
                    suit = cardElement.dataset.cardSuit;
                }

                if (cardElement.dataset.cardValue) {
                    value = cardElement.dataset.cardValue;
                }

                if (cardElement.dataset.card) {
                    const dataCard = cardElement.dataset.card;
                    if (dataCard && dataCard.length >= 2) {
                        const suitLetter = dataCard.charAt(0).toUpperCase();
                        const valueStr = dataCard.substring(1);

                        if (suitLetter === 'P') suit = 'Rock';
                        else if (suitLetter === 'C') suit = 'Paper';
                        else if (suitLetter === 'F') suit = 'Scissors';

                        value = valueStr;
                    }
                }
            }

            // Prova a determinare il seme e il valore dall'immagine
            if ((!suit || !value) && cardElement.querySelector('img')) {
                const cardImage = cardElement.querySelector('img');
                const cardSrc = cardImage.src || '';

                // Determina il seme dall'URL dell'immagine
                if (cardSrc.includes('/P') || cardSrc.toLowerCase().includes('pietra')) {
                    suit = 'Rock';
                } else if (cardSrc.includes('/C') || cardSrc.toLowerCase().includes('carta')) {
                    suit = 'Paper';
                } else if (cardSrc.includes('/F') || cardSrc.toLowerCase().includes('forbice')) {
                    suit = 'Scissors';
                }

                // Determina il valore dall'URL dell'immagine
                const valueMatch = cardSrc.match(/[PCF](\d+)\.(png|webp)/i);
                if (valueMatch && valueMatch[1]) {
                    value = valueMatch[1];
                }
            }

            // Se abbiamo sia il seme che il valore, restituisci la carta
            if (suit && value) {
                return {
                    suit: suit,
                    value: value,
                    isNeutral: cardElement.classList.contains('neutral-card')
                };
            }

            return null;
        } catch (error) {
            console.error('[PSN] Errore nell\'estrazione delle informazioni della carta:', error);
            return null;
        }
    }

    // Determina il colore di una carta
    function getCardColor(cardElement) {
        if (!cardElement) return 'unknown';

        if (cardElement.classList.contains('white-card')) {
            return 'white';
        } else if (cardElement.classList.contains('black-card')) {
            return 'black';
        }

        // Se non ha classi di colore, prova a determinarlo dal turno corrente
        return state.currentPlayer;
    }

    // Registra una nuova mossa
    function registerMove(card, position, color, gainedVertexControl, cardsRemaining) {
        // Determina se è il turno del bianco o del nero
        const isWhite = color === 'white';

        console.log(`[PSN DEBUG] Inizio registerMove: card=${JSON.stringify(card)}, position=${position} (mossa), color=${color}, gainedVertexControl=${gainedVertexControl}, cardsRemaining=${cardsRemaining}, state.currentTurn (prima)=${state.currentTurn}, state.currentPlayer=${state.currentPlayer}`);

        // DEBUG AGGIUNTIVO per state.pendingVertexControl
        console.log(`[PSN DEBUG RegisterMove Pre-Check] state.pendingVertexControl attuale:`, JSON.stringify(state.pendingVertexControl));
        if (state.pendingVertexControl) {
            console.log(`[PSN DEBUG RegisterMove Pre-Check] state.pendingVertexControl.length: ${state.pendingVertexControl.length}`);
        } else {
            console.log(`[PSN DEBUG RegisterMove Pre-Check] state.pendingVertexControl è null o undefined.`);
        }

        // Se è il bianco a giocare, incrementa il numero del turno.
        if (isWhite) {
            const oldTurn = state.currentTurn;
            state.currentTurn++;
            console.log(`[PSN DEBUG] Dentro if(isWhite): state.currentTurn era ${oldTurn}, ora è ${state.currentTurn}`);
        } else {
            console.log(`[PSN DEBUG] Dentro else (isWhite è false): state.currentTurn non modificato, resta ${state.currentTurn}`);
        }

        // Crea l'oggetto mossa
        const move = {
            turnNumber: state.currentTurn,
            isWhite: isWhite,
            card: card,
            position: position, // Posizione della mossa
            gainedVertexControl: gainedVertexControl || false,
            cardsRemaining: cardsRemaining,
            timestamp: new Date()
        };

        // Verifica se ci sono informazioni di controllo vertice in attesa per questa mossa
        // Questo blocco determina se LA MOSSA ATTUALE (card@position) deve ricevere l'asterisco
        // a causa di un controllo di vertice (registrato in pendingVertexControl) causato da QUESTA o una mossa immediatamente precedente.
        if (state.pendingVertexControl && state.pendingVertexControl.length > 0) {
            console.log(`[PSN DEBUG RegisterMove Pending] Controllo per vertex control in attesa (${state.pendingVertexControl.length}). Mossa corrente: ${getCardNotation(card)}@${position}, Giocatore: ${isWhite ? 'Bianco' : 'Nero'}`);

            // Filtra i controlli pendenti per il giocatore corrente
            const pendingControlForPlayer = state.pendingVertexControl.filter(vc => vc.isWhite === isWhite);

            if (pendingControlForPlayer.length > 0) {
                console.log(`[PSN DEBUG RegisterMove Pending] Trovati ${pendingControlForPlayer.length} controlli vertice in attesa per ${isWhite ? 'bianco' : 'nero'}. Lista:`, JSON.stringify(pendingControlForPlayer));

                // Cerca un pending control la cui posizione del VERTICE corrisponda alla posizione della MOSSA CORRENTE.
                // Questo significa che la mossa corrente su `position` ha causato il controllo del vertice `position`.
                const exactMatchMoveToPendingVertex = pendingControlForPlayer.find(vc => vc.position === position);

                if (exactMatchMoveToPendingVertex) {
                    console.log(`[PSN DEBUG RegisterMove Pending] Corrispondenza ESATTA: la mossa corrente su ${position} ha causato controllo del vertice ${position}. Applico *`);
                    move.gainedVertexControl = true;

                    // Rimuovi questo specifico pending control (quello per vc.position === position e giocatore corrente)
                    const originalPendingCount = state.pendingVertexControl.length;
                    state.pendingVertexControl = state.pendingVertexControl.filter(vc =>
                        !(vc.isWhite === isWhite && vc.position === position) // Rimuove quello per il vertice `position` del giocatore corrente
                    );
                    console.log(`[PSN DEBUG RegisterMove Pending] Rimosso pending control per vertice ${position} (giocatore ${isWhite}). Da ${originalPendingCount} a ${state.pendingVertexControl.length}`);
                }
                // Se NESSUN pending control corrisponde ESATTAMENTE alla posizione della mossa corrente,
                // significa che questa mossa NON ha causato il controllo di un vertice NELLA STESSA POSIZIONE.
                // Tuttavia, potrebbe aver causato il controllo di un vertice DIVERSO, o un controllo pendente è lì da una notifica precedente.
                // Applichiamo il controllo pendente più RECENTE per questo giocatore a QUESTA mossa.
                // Questo assume che la mossa corrente sia responsabile per il più recente controllo di vertice non ancora assegnato per questo giocatore.
                else {
                    pendingControlForPlayer.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
                    const mostRecentPendingControl = pendingControlForPlayer[0]; // Questo è il controllo di vertice (es. f6) che è in attesa

                    console.log(`[PSN DEBUG RegisterMove Pending] Nessuna corrispondenza esatta tra posizione del pending control (es. ${mostRecentPendingControl.position}) e posizione mossa corrente (${position}).`);
                    console.log(`[PSN DEBUG RegisterMove Pending] Applico il controllo del vertice ${mostRecentPendingControl.position} (giocatore ${isWhite ? 'bianco' : 'nero'}) a QUESTA mossa (${getCardNotation(card)}@${position}).`);
                    move.gainedVertexControl = true;

                    // Rimuovi il controllo pendente specifico che abbiamo appena usato (il più recente)
                    const originalPendingCount = state.pendingVertexControl.length;
                    state.pendingVertexControl = state.pendingVertexControl.filter(vc =>
                        !(vc.isWhite === mostRecentPendingControl.isWhite &&
                          vc.position === mostRecentPendingControl.position &&
                          new Date(vc.timestamp).getTime() === new Date(mostRecentPendingControl.timestamp).getTime())
                    );
                    console.log(`[PSN DEBUG RegisterMove Pending] Rimosso il pending control USATO (${mostRecentPendingControl.position}, giocatore ${isWhite}). Da ${originalPendingCount} a ${state.pendingVertexControl.length}`);
                }
            } else {
                 console.log(`[PSN DEBUG RegisterMove Pending] Nessun pending control trovato specificamente per il giocatore corrente (${isWhite ? 'bianco' : 'nero'}) tra quelli in state.pendingVertexControl.`);
            }
        } else {
            console.log(`[PSN DEBUG RegisterMove Pending] state.pendingVertexControl è vuoto o non definito al momento del check.`);
        }

        console.log(`[PSN DEBUG] Oggetto 'move' creato (dopo check pending): ${JSON.stringify(move)}`);

        // Aggiungi alla cronologia
        state.moveHistory.push(move);

        // Imposta il flag che il gioco è iniziato (usato per non registrare carte iniziali come pescate)
        if (!state.gameStarted) {
            state.gameStarted = true;
            state.initialCardsDrawn = true; // Consideriamo le carte iniziali come distribuite
            console.log('[PSN] Gioco iniziato - prima mossa registrata');
        }

        // --- INIZIO MODIFICA ---
        // Se è la prima mossa effettiva della partita e il cambio tab non è ancora avvenuto
        if (state.moveHistory.length === 1 && !state.firstMoveMadeSwitchDone) {
            console.log('[PSN] Prima mossa rilevata, cambio a vista Notazioni.');
            state.showHandsOnly = false;
            state.firstMoveMadeSwitchDone = true;

            // Aggiorna il testo del pulsante toggle
            const gridToggleBtn = document.querySelector('.psn-grid-toggle-btn');
            if (gridToggleBtn) {
                gridToggleBtn.innerHTML = '<i class="fas fa-hand-paper"></i> Mostra Mani'; // Ora dovrebbe mostrare "Mostra Mani"
            }
            // updateGridDisplay() sarà chiamato da generateNotation()
        }
        // --- FINE MODIFICA ---

        // Cambia il giocatore corrente per la prossima mossa
        state.currentPlayer = isWhite ? 'black' : 'white';

        // Log arricchito con informazioni sul controllo del vertice
        const controlMsg = move.gainedVertexControl ? ' (controllo vertice ottenuto)' : '';
        console.log(`[PSN] Mossa registrata (con debug): Turno ${move.turnNumber}, ${isWhite ? 'Bianco' : 'Nero'} - ${card.suit} ${card.value} su ${position}${controlMsg}. Prossimo giocatore: ${state.currentPlayer}. StateTurn: ${state.currentTurn}`);

        // Rigenera la notazione dopo aver registrato la mossa
        generateNotation();
    }

    // NUOVA FUNZIONE: Registra mosse autorevolmente dal server
    function registerMoveFromServer(gameState) {
        console.log('[PSN SERVER] === REGISTRAZIONE AUTORITATIVA DAL SERVER ===');
        
        if (!gameState || !gameState.board || !gameState.players) {
            console.log('[PSN SERVER] Stato del gioco incompleto, skip registrazione');
            return;
        }
        
        // Confronta lo stato attuale del board con quello precedente per rilevare nuove carte
        if (!state.lastServerBoardState) {
            // Prima volta che riceviamo lo stato dal server
            console.log('[PSN SERVER] Prima ricezione stato server - controllo mosse esistenti');

            // ✅ CORREZIONE: Anche nella prima ricezione, controlla se ci sono mosse reali da registrare
            // Distingui tra carta iniziale (setup) e mosse reali dei giocatori
            const initialPosition = gameState.initialPosition;
            const realMoves = [];

            Object.entries(gameState.board).forEach(([position, card]) => {
                if (card && card.id && position !== initialPosition) {
                    // Questa è una mossa reale, non la carta iniziale
                    console.log(`[PSN SERVER] Prima ricezione - mossa reale rilevata: ${card.id} in ${position} (owner: ${card.ownerColor})`);

                    // Determina il numero di carte rimanenti
                    let cardsRemaining = undefined;
                    const cardColor = card.ownerColor;

                    // Trova il giocatore corrispondente al colore della carta
                    for (const [playerId, playerData] of Object.entries(gameState.players)) {
                        if (playerData.color === cardColor && playerData.handSize !== undefined) {
                            cardsRemaining = playerData.handSize;
                            console.log(`[PSN SERVER] Prima ricezione - carte rimanenti per ${cardColor} (${playerId}): ${cardsRemaining}`);
                            break;
                        }
                    }

                    realMoves.push({
                        card: {
                            suit: card.suit,
                            value: card.value,
                            id: card.id
                        },
                        position: position,
                        color: cardColor,
                        cardsRemaining: cardsRemaining,
                        timestamp: Date.now()
                    });
                }
            });

            // Registra le mosse reali trovate nella prima ricezione
            if (realMoves.length > 0) {
                console.log(`[PSN SERVER] Prima ricezione - registrazione di ${realMoves.length} mosse reali`);
                realMoves.forEach(moveData => {
                    console.log(`[PSN SERVER] Registrando mossa autoritativa: ${moveData.card.id} in ${moveData.position} (${moveData.color}, ${moveData.cardsRemaining} carte rimanenti)`);
                    registerMove(
                        moveData.card,
                        moveData.position,
                        'mossa',
                        moveData.color,
                        false, // gainedVertexControl - sarà calcolato internamente
                        moveData.cardsRemaining
                    );
                });
            }

            // Salva lo stato per confronti futuri
            state.lastServerBoardState = JSON.parse(JSON.stringify(gameState.board));

            // Aggiorna i conteggi delle mani dal server
            state.previousHandSizes = {};
            Object.entries(gameState.players).forEach(([playerId, playerData]) => {
                if (playerData.handSize !== undefined) {
                    state.previousHandSizes[playerId] = playerData.handSize;
                }
            });

            // Se abbiamo registrato mosse, forza aggiornamento della griglia
            if (realMoves.length > 0) {
                console.log('[PSN SERVER] Forzando aggiornamento griglia dopo prima ricezione con mosse');
                setTimeout(() => {
                    updateGridDisplay();
                }, 100);
            }

            return;
        }
        
        // Rileva nuove carte sul board confrontando con lo stato precedente
        const newMoves = [];
        Object.entries(gameState.board).forEach(([position, card]) => {
            if (card && card.id) {
                const previousCard = state.lastServerBoardState[position];
                
                // Se non c'era una carta prima in questa posizione, è una nuova mossa
                if (!previousCard && position !== gameState.initialPosition) {
                    console.log(`[PSN SERVER] Nuova carta rilevata: ${card.id} in ${position} (owner: ${card.ownerColor})`);
                    
                    // Determina il numero di carte rimanenti usando i dati autoritativi dal server
                    let cardsRemaining = undefined;
                    const cardColor = card.ownerColor;
                    
                    // Trova il giocatore corrispondente al colore della carta
                    for (const [playerId, playerData] of Object.entries(gameState.players)) {
                        if (playerData.color === cardColor && playerData.handSize !== undefined) {
                            cardsRemaining = playerData.handSize;
                            console.log(`[PSN SERVER] Carte rimanenti per ${cardColor} (${playerId}): ${cardsRemaining}`);
                            break;
                        }
                    }
                    
                    // Se non troviamo handSize nel server, usa i dati precedenti o conteggio locale
                    if (cardsRemaining === undefined && state.previousHandSizes) {
                        for (const [playerId, handSize] of Object.entries(state.previousHandSizes)) {
                            if (window.permanentPlayerColors && window.permanentPlayerColors[playerId] === cardColor) {
                                cardsRemaining = handSize;
                                console.log(`[PSN SERVER] Usando handSize memorizzato per ${cardColor}: ${cardsRemaining}`);
                                break;
                            }
                        }
                    }
                    
                    // Fallback al conteggio locale se non abbiamo altre opzioni
                    if (cardsRemaining === undefined) {
                        cardsRemaining = cardColor === 'white' ? 
                            state.handCards.white.length : 
                            state.handCards.black.length;
                        console.log(`[PSN SERVER] Fallback al conteggio locale per ${cardColor}: ${cardsRemaining}`);
                    }
                    
                    newMoves.push({
                        card: {
                            suit: card.suit,
                            value: card.value,
                            id: card.id
                        },
                        position: position,
                        color: cardColor,
                        cardsRemaining: cardsRemaining,
                        timestamp: Date.now()
                    });
                }
            }
        });
        
        // **PROTEZIONE**: Segnala che stiamo registrando dal server per evitare interferenze
        state.isRegisteringFromServer = true;
        
        // Registra le nuove mosse
        newMoves.forEach(move => {
            console.log(`[PSN SERVER] Registrando mossa autoritativa: ${move.card.id} in ${move.position} (${move.color}, ${move.cardsRemaining} carte rimanenti)`);
            
            // **CORREZIONE CONTROLLO VERTICE**: Verifica se questa posizione è un vertice
            const isVertexPosition = ['a1', 'f1', 'a6', 'f6'].includes(move.position);
            let gainedVertexControl = false;
            
            if (isVertexPosition) {
                // Controlla se il giocatore ha effettivamente ottenuto il controllo del vertice
                // (la carta non deve essere neutral per controllare il vertice)
                const cardOnBoard = gameState.board[move.position];
                if (cardOnBoard && cardOnBoard.ownerColor !== 'neutral') {
                    gainedVertexControl = true;
                    console.log(`[PSN SERVER] ✅ CONTROLLO VERTICE rilevato in ${move.position} per ${move.color}`);
                } else {
                    console.log(`[PSN SERVER] Carta in vertice ${move.position} è neutral, nessun controllo`);
                }
            } else {
                // Verifica anche se c'è un controllo vertice pending per questa mossa
                if (state.pendingVertexControl && state.pendingVertexControl.length > 0) {
                    const isWhite = move.color === 'white';
                    const pendingControlForPlayer = state.pendingVertexControl.filter(vc => vc.isWhite === isWhite);
                    
                    if (pendingControlForPlayer.length > 0) {
                        // Applica il controllo vertice più recente per questo giocatore
                        gainedVertexControl = true;
                        console.log(`[PSN SERVER] Applicato controllo vertice pending a mossa ${move.card.id} (${move.color})`);
                        
                        // Rimuovi il controllo pending usato
                        const mostRecent = pendingControlForPlayer[pendingControlForPlayer.length - 1];
                        state.pendingVertexControl = state.pendingVertexControl.filter(vc => 
                            !(vc.isWhite === mostRecent.isWhite && vc.position === mostRecent.position && vc.timestamp === mostRecent.timestamp)
                        );
                    }
                }
            }
            
            // Chiama registerMove con tutti i parametri corretti
            registerMove(move.card, move.position, move.color, gainedVertexControl, move.cardsRemaining);
        });
        
        // Aggiorna lo stato del board precedente per i confronti futuri
        state.lastServerBoardState = JSON.parse(JSON.stringify(gameState.board));
        
        // Aggiorna i conteggi delle mani per i confronti futuri
        if (!state.previousHandSizes) state.previousHandSizes = {};
        Object.entries(gameState.players).forEach(([playerId, playerData]) => {
            if (playerData.handSize !== undefined) {
                state.previousHandSizes[playerId] = playerData.handSize;
            }
        });
        
        // **PROTEZIONE FINALE**: Rimuovi la protezione e forza aggiornamento finale
        setTimeout(() => {
            state.isRegisteringFromServer = false;
            
            // **FORZA AGGIORNAMENTO**: Assicurati che la griglia PSN sia aggiornata dopo le registrazioni dal server
            if (newMoves.length > 0) {
                console.log('[PSN SERVER] Forzando aggiornamento finale della griglia PSN dopo registrazione dal server');
                updateGridDisplay();
            }
        }, 50); // Breve delay per assicurarsi che tutte le operazioni siano completate
        
        console.log(`[PSN SERVER] Registrazione completata: ${newMoves.length} nuove mosse elaborate`);
    }

    // Cattura le carte in mano dai giocatori
    function captureHandCards() {
        try {
            // Salva le carte precedenti prima di aggiornare
            const previousWhiteCards = [...state.handCards.white];
            const previousBlackCards = [...state.handCards.black];

            // Cerca le mani dei giocatori
            const player1Hand = document.querySelector(config.player1HandSelector);
            const player2Hand = document.querySelector(config.player2HandSelector);

            // Resetta le liste
            state.handCards.white = [];
            state.handCards.black = [];

            // Cattura le carte della mano del giocatore 1 (bianco)
            if (player1Hand) {
                const cards = player1Hand.querySelectorAll('.card, .game-card');
                cards.forEach(card => {
                    const cardInfo = extractCardInfo(card);
                    if (cardInfo) {
                        state.handCards.white.push(getCardNotation(cardInfo));
                    }
                });
            }

            // Cattura le carte della mano del giocatore 2 (nero)
            if (player2Hand) {
                const cards = player2Hand.querySelectorAll('.card, .game-card');
                cards.forEach(card => {
                    const cardInfo = extractCardInfo(card);
                    if (cardInfo) {
                        state.handCards.black.push(getCardNotation(cardInfo));
                    }
                });
            }

            // Verifica se ci sono nuove carte in mano (possibile pesca)
            // Controlla se abbiamo già registrato le carte iniziali
            const hadPreviousCards = previousWhiteCards.length > 0 || previousBlackCards.length > 0;

            // Verifica se è il set iniziale di carte
            const isInitialHand = !state.initialCardsDrawn && state.moveHistory.length === 0;

            // Se sono le carte iniziali, segna che sono state distribuite ma non registrarle
            if (isInitialHand) {
                state.initialCardsDrawn = true;

            }

            // Procedi solo se non sono le carte iniziali e abbiamo già avuto carte precedenti
            if (!isInitialHand && hadPreviousCards) {
                // CORREZIONE: Protezione per modalità multiplayer - evita false rilevazioni di pescate
                const isMultiplayerMode = window.currentGameState?.mode === 'online' || 
                                        window.myPlayerId || 
                                        window.permanentPlayerNames ||
                                        (window.socket && window.socket.connected) ||
                                        window.isOnlineMode || 
                                        (window.multiplayer && window.multiplayer.isConnected);
                
                // In modalità multiplayer, usa una logica più conservativa per rilevare pescate
                if (isMultiplayerMode) {
                    // THROTTLING: Log questo messaggio solo una volta ogni 10 secondi per evitare spam
                    const now = Date.now();
                    if (!state.lastMultiplayerProtectionLogTime || (now - state.lastMultiplayerProtectionLogTime) > 10000) {
                        console.log('[PSN] Modalità multiplayer rilevata - applicando protezione pescate spurie');
                        state.lastMultiplayerProtectionLogTime = now;
                    }
                    
                    // **PROTEZIONE REGISTRAZIONE SERVER**: Skip se stiamo registrando dal server
                    if (state.isRegisteringFromServer) {
                        // THROTTLING: Log questo messaggio solo una volta ogni 5 secondi per evitare spam
                        if (!state.lastSkipDrawDetectionLogTime || (now - state.lastSkipDrawDetectionLogTime) > 5000) {
                            console.log('[PSN] Skip rilevazione pescate - registrazione dal server in corso');
                            state.lastSkipDrawDetectionLogTime = now;
                        }
                        return;
                    }
                    
                    // Solo se l'incremento è significativo (>=2 carte) o se è chiaramente una pesca
                    const whiteIncrement = state.handCards.white.length - previousWhiteCards.length;
                    const blackIncrement = state.handCards.black.length - previousBlackCards.length;
                    
                    // Protezione: in multiplayer, non rilevare pescate di singole carte che potrebbero essere
                    // dovute a sincronizzazione temporanea dello stato
                    if (whiteIncrement >= 2) {
                        const newCards = state.handCards.white.filter(card => !previousWhiteCards.includes(card));
                        if (newCards.length > 0) {
                            console.log('[PSN] MULTIPLAYER: Rilevata pesca significativa bianco (>=2 carte):', newCards);
                            newCards.forEach(newCard => registerCardDraw(newCard, true));
                        }
                    } else if (whiteIncrement === 1) {
                        const newCards = state.handCards.white.filter(card => !previousWhiteCards.includes(card));
                        if (newCards.length === 1) {
                            console.log('[PSN] MULTIPLAYER: 🎯 PESCATA SINGOLA BIANCO rilevata:', newCards[0]);
                            // Registra la pescata con timestamp per la visualizzazione
                            state.lastDrawnCard = `Hai pescato: ${newCards[0]}`;
                            state.lastCardDrawTime = Date.now();
                            // Registra anche nella cronologia standard
                            registerCardDraw(newCards[0], true);
                            // Aggiorna la visualizzazione PSN
                            updateGridDisplay();
                        } else {
                            console.log('[PSN] MULTIPLAYER: Incremento singola carta bianco ignorato (possibile sincronizzazione)');
                        }
                    }
                    
                    if (blackIncrement >= 2) {
                        const newCards = state.handCards.black.filter(card => !previousBlackCards.includes(card));
                        if (newCards.length > 0) {
                            console.log('[PSN] MULTIPLAYER: Rilevata pesca significativa nero (>=2 carte):', newCards);
                            newCards.forEach(newCard => registerCardDraw(newCard, false));
                        }
                    } else if (blackIncrement === 1) {
                        const newCards = state.handCards.black.filter(card => !previousBlackCards.includes(card));
                        if (newCards.length === 1) {
                            console.log('[PSN] MULTIPLAYER: 🎯 PESCATA SINGOLA NERO rilevata:', newCards[0]);
                            // Registra la pescata con timestamp per la visualizzazione
                            state.lastDrawnCard = `Hai pescato: ${newCards[0]}`;
                            state.lastCardDrawTime = Date.now();
                            // Registra anche nella cronologia standard
                            registerCardDraw(newCards[0], false);
                            // Aggiorna la visualizzazione PSN
                            updateGridDisplay();
                        } else {
                            console.log('[PSN] MULTIPLAYER: Incremento singola carta nero ignorato (possibile sincronizzazione)');
                        }
                    }
                    
                    // NUOVO: Controlla se l'avversario ha pescato una carta (incremento handSize)
                    // Questo permette di visualizzare la pescata anche nel client dell'avversario
                    if (window.currentGameState && window.currentGameState.players) {
                        // Inizializza previousHandSizes se non esiste
                        if (!state.previousHandSizes) state.previousHandSizes = {};
                if (!state.previousHandCards) state.previousHandCards = {};
                        
                        for (const [playerId, playerData] of Object.entries(window.currentGameState.players)) {
                            if (playerData.handSize && state.previousHandSizes[playerId] !== undefined) {
                                const handSizeIncrement = playerData.handSize - state.previousHandSizes[playerId];
                                
                                if (handSizeIncrement === 1) {
                                    // Un giocatore ha pescato una carta
                                    const isMyPlayer = playerId === window.myPlayerId;
                                    const playerColor = playerData.color;
                                    
                                    if (!isMyPlayer) {
                                        console.log(`[PSN] MULTIPLAYER: 🎯 AVVERSARIO (${playerColor}) ha pescato una carta! handSize: ${state.previousHandSizes[playerId]} -> ${playerData.handSize}`);
                                        
                                        // NOTA: In modalità multiplayer, il sistema PSN standard già rileva tutte le pescate
                                        // incluse quelle degli avversari tramite captureHandCards(), quindi NON registriamo
                                        // duplicati qui. Limitiamoci solo alla visualizzazione e al cambio di modalità.
                                        
                                        console.log(`[PSN] MULTIPLAYER: Pescata avversario rilevata ma NON registrata (già gestita dal sistema standard)`);
                                        
                                        // Simula la carta pescata per la visualizzazione
                                        const playerName = playerColor === 'white' ? 'Bianco' : 'Nero';
                                        state.lastDrawnCard = `${playerName} ha pescato una carta`;
                                        state.lastCardDrawTime = Date.now();
                                        
                                        // Se siamo in modalità visualizzazione mani, passiamo alla modalità notazioni
                                        if (state.showHandsOnly) {
                                            console.log('[PSN] Avversario ha pescato, cambio automatico a vista Notazioni.');
                                            state.showHandsOnly = false;
                                            
                                            // Aggiorna il testo del pulsante toggle se esiste
                                            const gridToggleBtn = document.querySelector('.psn-grid-toggle-btn');
                                            if (gridToggleBtn) {
                                                gridToggleBtn.innerHTML = '<i class="fas fa-hand-paper"></i> Mostra Mani';
                                            }
                                        }
                                        
                                        // Aggiorna la visualizzazione PSN
                                        updateGridDisplay();
                                    }
                                }
                            }
                        }
                        
                        // Aggiorna i handSize precedenti per il controllo successivo
                        for (const [playerId, playerData] of Object.entries(window.currentGameState.players)) {
                            state.previousHandSizes[playerId] = playerData.handSize || 0;
                            // Memorizza anche le mani precedenti per il confronto delle carte pescate
                            state.previousHandCards[playerId] = playerData.hand ? [...playerData.hand] : [];
                        }
                    }
                } else {
                    // Modalità locale - usa la logica originale
                    // Verifica se ci sono nuove carte in mano (possibile pesca)
                    if (state.handCards.white.length > previousWhiteCards.length) {
                        // Trova la nuova carta confrontando gli array
                        const newCards = state.handCards.white.filter(card => !previousWhiteCards.includes(card));

                        if (newCards.length > 0) {
                            // Registra la pesca per ogni nuova carta
                            newCards.forEach(newCard => {
                                console.log('[PSN] Rilevata nuova carta nella mano del bianco:', newCard);
                                // Registriamo l'evento di pesca carta
                                registerCardDraw(newCard, true);
                            });
                        }
                    }

                    if (state.handCards.black.length > previousBlackCards.length) {
                        // Trova la nuova carta confrontando gli array
                        const newCards = state.handCards.black.filter(card => !previousBlackCards.includes(card));

                        if (newCards.length > 0) {
                            // Registra la pesca per ogni nuova carta
                            newCards.forEach(newCard => {
                                console.log('[PSN] Rilevata nuova carta nella mano del nero:', newCard);
                                // Registriamo l'evento di pesca carta
                                registerCardDraw(newCard, false);
                            });
                        }
                    }
                }
            } else {
                // Se siamo all'inizio del gioco, non registriamo le carte come pescate
            }
        } catch (error) {
            console.error('[PSN] Errore durante la cattura delle carte in mano:', error);
        }
    }

    // Genera la notazione PSN
    function generateNotation() {
        try {
            // Controllo rapido se ci sono dati da mostrare
            if (!state.initialPosition && Object.keys(state.boardState).length === 0) {
                return null;
            }

            // Inizio con la riga di setup (formattata per allinearsi meglio)
            const setupPosition = state.initialPosition || Object.keys(state.boardState)[0] || 'e3';
            const whiteSetup = `${formatHand(state.handCards.white)}:white`;
            const blackSetup = `${formatHand(state.handCards.black)}:black`;
            let notation = `0.${setupPosition}|${whiteSetup.padEnd(25, ' ')};${blackSetup}\n`;

            // Aggiungi le mosse dalla cronologia
            const movesByTurn = {};

            // Process move history

            state.moveHistory.forEach(move => {
                if (!movesByTurn[move.turnNumber]) {
                    movesByTurn[move.turnNumber] = { white: null, black: null };
                }

                if (move.isWhite) {
                    movesByTurn[move.turnNumber].white = move;
                } else {
                    movesByTurn[move.turnNumber].black = move;
                }
            });

            // Genera la notazione per ogni turno con allineamento delle colonne
            Object.keys(movesByTurn)
                .sort((a, b) => parseInt(a) - parseInt(b))
                .forEach(turnNumber => {
                    const turn = movesByTurn[turnNumber];

                    // Numero di turno sempre con due cifre per allineamento
                    let formattedTurnNum = turnNumber.toString().padStart(2, ' ');
                    let line = `${formattedTurnNum}.`;

                    // Mossa del bianco
                    let whiteMove = '';
                    if (turn.white) {
                        const card = turn.white.card;
                        const cardNotation = getCardNotation(card);
                        const position = turn.white.position;

                        // Usa le carte rimanenti memorizzate se disponibili, altrimenti usa i dati del server o stima locale
                        let whiteRemaining = turn.white.cardsRemaining;
                        if (whiteRemaining === undefined && state.previousHandSizes) {
                            // Cerca il conteggio dal server per il giocatore bianco
                            for (const [playerId, handSize] of Object.entries(state.previousHandSizes)) {
                                if (window.permanentPlayerColors && window.permanentPlayerColors[playerId] === 'white') {
                                    whiteRemaining = handSize;
                                    break;
                                }
                            }
                        }
                        if (whiteRemaining === undefined) {
                            whiteRemaining = Math.max(0, state.handCards.white.length);
                        }

                        // Aggiungi un asterisco se la mossa ha portato al controllo di un vertice
                        whiteMove = `${cardNotation}|${position}|${whiteRemaining}`;
                        if (turn.white.gainedVertexControl) {
                            whiteMove += "*"; // Aggiungi l'asterisco per indicare controllo vertice
                        }
                    } else {
                        whiteMove = '...';
                    }

                    // Aggiungi la mossa del bianco con spazio minimo per garantire leggibilità
                    line += whiteMove;

                    // Separatore con spazi minini
                    line += ' ; '; // Usa sempre punto e virgola per separare

                    // Mossa del nero
                    let blackMove = '';
                    if (turn.black) {
                        const card = turn.black.card;
                        const cardNotation = getCardNotation(card);
                        const position = turn.black.position;

                        // Usa le carte rimanenti memorizzate se disponibili, altrimenti usa i dati del server o stima locale
                        let blackRemaining = turn.black.cardsRemaining;
                        if (blackRemaining === undefined && state.previousHandSizes) {
                            // Cerca il conteggio dal server per il giocatore nero
                            for (const [playerId, handSize] of Object.entries(state.previousHandSizes)) {
                                if (window.permanentPlayerColors && window.permanentPlayerColors[playerId] === 'black') {
                                    blackRemaining = handSize;
                                    break;
                                }
                            }
                        }
                        if (blackRemaining === undefined) {
                            blackRemaining = Math.max(0, state.handCards.black.length);
                        }

                        // Aggiungi un asterisco se la mossa ha portato al controllo di un vertice
                        blackMove = `${cardNotation}|${position}|${blackRemaining}`;
                        if (turn.black.gainedVertexControl) {
                            blackMove += "*"; // Aggiungi l'asterisco per indicare controllo vertice
                        }
                    } else {
                        blackMove = '...';
                    }

                    // Aggiungi la mossa del nero
                    line += blackMove;

                    notation += line + '\n';
                });

            // Rimuovi i tre puntini se una delle mosse (bianco o nero) è vuota
            notation = notation.replace(/; \.\.\./g, ';')
                               .replace(/\.\.\. ;/g, ';');

            // Rimuovi i tre puntini rimasti se sono gli unici nella riga
            // es. "1. ; " (se entrambe le mosse erano '...')
            notation = notation.replace(/^(\d+\.)\s*;\s*$/gm, '$1');


            // Pulisci eventuali righe vuote risultanti
            notation = notation.split('\n').filter(line => line.trim() !== '').join('\n');

            // Aggiorna lo stato e la visualizzazione
            state.psnContent = notation;
            updateStandardComponents(notation); // Aggiorna componenti standard (se presenti)
            updateNotationDisplay(notation);    // Aggiorna visualizzazione principale
            updateNotationsHistory(notation);   // Aggiorna la cronologia per la griglia
            updateGridDisplay();                // Aggiorna la griglia


            return notation;

        } catch (error) {
            console.error('[PSN] Errore durante la generazione della notazione:', error);
            return null;
        }
    }

    // Aggiorna i componenti PSN standard
    function updateStandardComponents(notation) {
        if (!notation) return;

        try {
            // Crea una versione standard (non formattata per colonne) per i componenti legacy
            const standardNotation = convertToStandardFormat(notation);

            // Aggiorna enhancedPSN se disponibile
            if (window.enhancedPSN && typeof window.enhancedPSN.updateFromExternalSource === 'function') {
                window.enhancedPSN.updateFromExternalSource(standardNotation);
            }

            // Aggiorna il visualizzatore PSN se disponibile
            if (window.psnVisualizer && typeof window.psnVisualizer.loadPSNString === 'function') {
                window.psnVisualizer.loadPSNString(standardNotation);
            }

            // Aggiorna tramite updatePSNVisualizer se disponibile
            if (typeof window.updatePSNVisualizer === 'function') {
                window.updatePSNVisualizer();
            }
        } catch (error) {
            console.error('[PSN] Errore durante l\'aggiornamento dei componenti standard:', error);
        }
    }

    // Converti la notazione formattata per colonne al formato standard per compatibilità
    function convertToStandardFormat(notation) {
        if (!notation) return '';

        // Dividi in righe e rimuovi la formattazione di allineamento
        const lines = notation.split('\n');
        const standardLines = lines.map(line => {
            // Se è una riga vuota, saltala
            if (!line.trim()) return '';

            // Rimuovi gli spazi extra (padding) usati per l'allineamento
            return line.replace(/\s+/g, ' ').trim();
        });

        return standardLines.join('\n');
    }

    // Aggiorna la visualizzazione della notazione
    function updateNotationDisplay(notation) {
        if (!state.components.contentEl) return;

        // Salva tutte le notazioni per la cronologia e la visualizzazione a griglia
        if (notation) {
            // Estrai le mosse dalla notazione
            updateNotationsHistory(notation);
        }

        // Visualizza la notazione standard nell'area principale (nascosta ma necessaria per compatibilità)
        state.components.contentEl.innerHTML = formatPSNForDisplay(notation);
        state.components.contentEl.style.display = 'none';

        // Cattura le carte in mano aggiornate
        captureHandCards();

        // Aggiorna la visualizzazione a griglia
        updateGridDisplay();
    }

    // Aggiorna la cronologia delle notazioni per la visualizzazione a griglia
    function updateNotationsHistory(notation) {
        if (!notation) return;

        try {
            const lines = notation.split('\n');
            const moveEntries = [];

            // Processa ogni riga
            for (let i = 1; i < lines.length; i++) {
                const line = lines[i].trim();
                if (!line) continue;

                // Parse della riga di notazione standard (con ;)
                const moveMatch = line.match(/^(\d+)\.\s*(.*?)\s*;\s*(.*)$/);
                if (moveMatch) {
                    const [, turnNumber, whiteMove, blackMove] = moveMatch;

                    // Crea un oggetto per la mossa
                    const moveEntry = {
                        turnNumber: parseInt(turnNumber),
                        white: whiteMove.trim(),
                        black: blackMove.trim(),
                        timestamp: new Date()
                    };

                    // Controlla se questa mossa è già presente
                    const existingIndex = state.notationsHistory.findIndex(m =>
                        m.turnNumber === moveEntry.turnNumber &&
                        m.white === moveEntry.white &&
                        m.black === moveEntry.black
                    );

                    if (existingIndex === -1) {
                        // Aggiungi la mossa alla cronologia
                        moveEntries.push(moveEntry);
                    }
                }
                // Gestione notazione pesca carta (solo un separatore |)
                else {
                    // Pattern per notazioni in vari formati di pesca carta
                    // Considera formati come:
                    // "12. P9|2" (solo white con Draw)
                    // "12. ... ; P9|2" (solo black con Draw)
                    // "12. Draw|2" o "12. ... ; Draw|2" (per notazione con "Draw")

                    // Primo pattern: per notazioni "12. P9|2" o "12. P9|2*" (per giocatore bianco)
                    // Incluso supporto per l'asterisco opzionale
                    const whiteCardDrawMatch = line.match(/^(\d+)\.\s*([A-Z][0-9]+\|\d+\*?|Draw\|\d+\*?)$/i);
                    if (whiteCardDrawMatch) {
                        const [, turnNumber, cardDraw] = whiteCardDrawMatch;

                        // Per notazioni del bianco, popola solo il campo white
                        const moveEntry = {
                            turnNumber: parseInt(turnNumber),
                            white: cardDraw.trim(),
                            black: '',
                            timestamp: new Date()
                        };

                        // Controlla se questa pesca è già presente
                        const existingIndex = state.notationsHistory.findIndex(m =>
                            m.turnNumber === moveEntry.turnNumber && m.white === moveEntry.white
                        );

                        if (existingIndex === -1) {
                            // Aggiungi la pesca alla cronologia
                            moveEntries.push(moveEntry);

                        }
                    }

                    // Secondo pattern: per notazioni con "12. ... ; P9|2" o "12. ... ; P9|2*" (per giocatore nero)
                    // Incluso supporto per l'asterisco opzionale
                    const blackCardDrawMatch = line.match(/^(\d+)\.\s*(?:\.\.\.)\s*;\s*([A-Z][0-9]+\|\d+\*?|Draw\|\d+\*?)$/i);
                    if (blackCardDrawMatch) {
                        const [, turnNumber, cardDraw] = blackCardDrawMatch;

                        // Per notazioni del nero, popola solo il campo black
                        const moveEntry = {
                            turnNumber: parseInt(turnNumber),
                            white: '...',
                            black: cardDraw.trim(),
                            timestamp: new Date()
                        };

                        // Controlla se questa pesca è già presente
                        const existingIndex = state.notationsHistory.findIndex(m =>
                            m.turnNumber === moveEntry.turnNumber && m.black === moveEntry.black
                        );

                        if (existingIndex === -1) {
                            // Aggiungi la pesca alla cronologia
                            moveEntries.push(moveEntry);

                        }
                    }

                    // Terzo pattern: per notazioni con formato generico con un solo separatore
                    if (!whiteCardDrawMatch && !blackCardDrawMatch) {
                        const genericDrawMatch = line.match(/^(\d+)\.\s*(.*?)(?:\s*;\s*(.*))?$/);
                        if (genericDrawMatch) {
                            const [, turnNumber, whitePart, blackPart] = genericDrawMatch;

                            // Verifica se le parti contengono notazioni di pesca (un solo separatore |)
                            // Nota: le mosse regolari hanno 2 separatori | (Carta|Posizione|Rimanenti)
                            // mentre le pescate hanno 1 separatore | (Carta|Rimanenti o Draw|Rimanenti)
                            // Ignoriamo l'eventuale asterisco alla fine che indica il controllo di un vertice
                            const whitePartTrimmed = whitePart ? whitePart.trim().replace(/\*$/, '') : '';
                            const isWhiteDraw = whitePartTrimmed !== '...' &&
                                                whitePartTrimmed.includes('|') &&
                                                whitePartTrimmed.split('|').length === 2;

                            // Applica lo stesso approccio per il nero, considerando l'asterisco opzionale
                            const blackPartTrimmed = blackPart ? blackPart.trim().replace(/\*$/, '') : '';
                            const isBlackDraw = blackPartTrimmed !== '' &&
                                               blackPartTrimmed.includes('|') &&
                                               blackPartTrimmed.split('|').length === 2;

                            if (isWhiteDraw || isBlackDraw) {
                                // Crea un oggetto per la mossa con pesca
                                const moveEntry = {
                                    turnNumber: parseInt(turnNumber),
                                    white: isWhiteDraw ? whitePart.trim() : '...',
                                    black: isBlackDraw ? blackPart.trim() : '',
                                    timestamp: new Date()
                                };

                                // Controlla se questa notazione è già presente
                                const existingIndex = state.notationsHistory.findIndex(m =>
                                    m.turnNumber === moveEntry.turnNumber &&
                                    m.white === moveEntry.white &&
                                    m.black === moveEntry.black
                                );

                                if (existingIndex === -1) {
                                    // Aggiungi la mossa alla cronologia
                                    moveEntries.push(moveEntry);

                                }
                            }
                        }
                    }
                }
            }

            // Aggiungi le nuove mosse alla cronologia
            if (moveEntries.length > 0) {
                state.notationsHistory = state.notationsHistory.concat(moveEntries);

                // Limita la dimensione della cronologia
                if (state.notationsHistory.length > config.gridConfig.maxItems) {
                    state.notationsHistory = state.notationsHistory.slice(-config.gridConfig.maxItems);
                }


            }
        } catch (error) {
            console.error('[PSN] Errore nell\'aggiornamento della cronologia delle notazioni:', error);
        }
    }

    // Aggiorna la visualizzazione a griglia
    function updateGridDisplay() {
        if (!state.components.gridContainer) return;

        try {
            // Rimossi console.log eccessivi per evitare spam
            
            // Imposta numero di colonne dalla config
            state.components.gridContainer.style.gridTemplateColumns = `repeat(${config.gridConfig.columns}, 1fr)`;

            // Svuota il contenitore
            state.components.gridContainer.innerHTML = '';

            // Se modalità visualizzazione mani è attiva
            if (state.showHandsOnly) {
                // Formatta le mani dei giocatori
                const whiteHandFormatted = formatHandForDisplay(state.handCards.white, 'Bianco');
                const blackHandFormatted = formatHandForDisplay(state.handCards.black, 'Nero');

                // Aggiungi elemento per la mano del bianco
                const whiteHandElement = document.createElement('div');
                whiteHandElement.className = 'psn-grid-item hand-display'; // Aggiunta classe per styling specifico
                whiteHandElement.innerHTML = whiteHandFormatted;
                state.components.gridContainer.appendChild(whiteHandElement);

                // Aggiungi elemento per la mano del nero
                const blackHandElement = document.createElement('div');
                blackHandElement.className = 'psn-grid-item hand-display'; // Aggiunta classe
                blackHandElement.innerHTML = blackHandFormatted;
                state.components.gridContainer.appendChild(blackHandElement);

                // === INIZIO MODIFICA ===
                // Aggiungi informazioni di Setup
                const setupInfoElement = document.createElement('div');
                setupInfoElement.className = 'psn-grid-item setup-info-display'; // Classe per styling
                setupInfoElement.id = 'psn-setup-info'; // ID per riferimento

                let setupNotationDisplay = 'N/A';



                if (state.initialPosition &&
                    state.boardState && state.boardState[state.initialPosition] &&
                    state.handCards) { // Assicurati che handCards sia disponibile

                    const initialCardData = state.boardState[state.initialPosition]; // Es: { suit: 'Paper', value: '8', isNeutral: true }

                    if (initialCardData && initialCardData.suit && initialCardData.value) {
                        const cardNotation = getCardNotation(initialCardData); // Es: "P1"
                        const positionNotation = state.initialPosition; // Es: "b2"

                        // Applica highlighting alla carta di setup e alla posizione separatamente
                        const highlightedCardSetup = highlightNotation(cardNotation);
                        const highlightedPositionSetup = `<span class="position-notation">${positionNotation}</span>`;

                        // Formatta le mani dei giocatori, evidenziando ogni carta
                        const whiteHandFormatted = highlightNotation(formatHand(state.handCards.white));
                        const blackHandFormatted = highlightNotation(formatHand(state.handCards.black));

                        // Costruiamo la stringa di notazione completa del setup secondo il nuovo formato
                        // Esempio: 0.P1:b2|F8/F2/F3/P8/C1:White;F1/F5/F4/F12/F11:Black
                        setupNotationDisplay = `0.${highlightedCardSetup}:${highlightedPositionSetup}|${whiteHandFormatted}:White;${blackHandFormatted}:Black`;


                    }
                }

                setupInfoElement.innerHTML = `<div class="setup-header">Setup:</div><div class="setup-notation">${setupNotationDisplay}</div>`;
                state.components.gridContainer.appendChild(setupInfoElement);
                // === FINE MODIFICA ===

                return; // Esci se mostriamo le mani
            }

            // Se non ci sono notazioni, controlla se c'è contenuto PSN da mostrare
            if (state.notationsHistory.length === 0) {
                // CORREZIONE: Controlla prima se c'è stata una pescata recente
                const now = Date.now();
                const lastDrawTime = state.lastCardDrawTime || 0;
                const isRecentDraw = (now - lastDrawTime) < 10000; // 10 secondi
                
                if (isRecentDraw && state.lastDrawnCard) {
                    
                    // Crea un solo grid-item che mostra solo la pescata
                    const gridItem = document.createElement('div');
                    gridItem.className = 'psn-grid-item psn-recent-draw-item current';
                    gridItem.innerHTML = `<div style="text-align: center; padding: 20px; font-size: 16px; font-weight: bold; color: #007bff; background: rgba(0,123,255,0.1); border-radius: 8px; border: 2px solid #007bff;">${state.lastDrawnCard}</div>`;
                    
                    state.components.gridContainer.appendChild(gridItem);
                    
                    // Auto-scroll alla fine per essere sicuri che sia visibile
                    state.components.gridContainer.scrollTop = state.components.gridContainer.scrollHeight;
                    
                    return; // NON mostrare altro contenuto
                }
                
                // Se abbiamo contenuto PSN valido, mostralo usando la funzione standard
                if (state.psnContent && state.psnContent.trim() && 
                    state.psnContent.trim() !== '0.e3||:white;:black') {
                    // Usa il contenuto PSN formattato invece della griglia vuota
                    const psnDisplay = document.createElement('div');
                    psnDisplay.className = 'psn-standard-display';
                    psnDisplay.innerHTML = formatPSNForDisplay(state.psnContent);
                    state.components.gridContainer.appendChild(psnDisplay);
                    return;
                }
                const emptyMsg = document.createElement('div');
                emptyMsg.className = 'psn-empty-message';
                emptyMsg.textContent = 'Nessuna mossa registrata.';
                state.components.gridContainer.appendChild(emptyMsg);
                return;
            }

            // Altrimenti, mostra la griglia delle notazioni raggruppate per turno

            // Raggruppa le notazioni per numero di turno
            const groupedNotations = {};
            state.notationsHistory.forEach(notation => {
                const turnNum = notation.turnNumber;
                if (!groupedNotations[turnNum]) {
                    groupedNotations[turnNum] = {
                        moves: { white: null, black: null }, // Per le mosse standard P|pos|R
                        draws: { white: [], black: [] },     // Per le pescate C|R o Draw|R
                        hasEntry: false // Flag per sapere se c'è almeno una voce per questo turno
                    };
                }

                groupedNotations[turnNum].hasEntry = true; // Segna che questo turno ha dati

                // Distingui tra mossa standard e pesca
                const isWhiteNotation = notation.white && notation.white !== ''; // Modificato da '...' a ''
                const isBlackNotation = notation.black && notation.black !== '';

                if (isWhiteNotation) {
                    const parts = notation.white.split('|');
                    // Mossa standard: Carta|Posizione|Rimanenti[*] (3 parti, asterisco facoltativo)
                    // Pesca: Carta|Rimanenti o Draw|Rimanenti (2 parti)
                    if (parts.length === 3 && parts[1].match(/^[a-f][1-6]$/)) { // Mossa standard con posizione valida
                         if (!groupedNotations[turnNum].moves.white) // Sovrascrivi solo se non c'è già una mossa
                            groupedNotations[turnNum].moves.white = notation.white;
                    } else if (parts.length === 2 || notation.white.startsWith('Draw|')) { // Pesca
                         if (!groupedNotations[turnNum].draws.white.includes(notation.white)) // Evita duplicati di pesca nello stesso turno
                            groupedNotations[turnNum].draws.white.push(notation.white);
                    } else { // Altro formato? Trattalo come mossa generica
                         if (!groupedNotations[turnNum].moves.white)
                            groupedNotations[turnNum].moves.white = notation.white;
                    }
                }
                 if (isBlackNotation) {
                    const parts = notation.black.split('|');
                     if (parts.length === 3 && parts[1].match(/^[a-f][1-6]$/)) { // Mossa standard con posizione valida
                         if (!groupedNotations[turnNum].moves.black)
                            groupedNotations[turnNum].moves.black = notation.black;
                     } else if (parts.length === 2 || notation.black.startsWith('Draw|')) { // Pesca
                         if (!groupedNotations[turnNum].draws.black.includes(notation.black))
                            groupedNotations[turnNum].draws.black.push(notation.black);
                     } else { // Altro?
                         if (!groupedNotations[turnNum].moves.black)
                            groupedNotations[turnNum].moves.black = notation.black;
                     }
                 }
            });

            // Ora itera sui turni raggruppati e crea un gridItem per turno
            Object.keys(groupedNotations)
                .map(num => parseInt(num)) // Converti in numeri per ordinamento corretto
                .sort((a, b) => a - b)     // Ordina i turni numericamente
                .forEach(turnNumber => {
                    const turnData = groupedNotations[turnNumber];

                    // Salta se per qualche motivo non ci sono voci per questo turno (dovrebbe essere raro)
                    if (!turnData.hasEntry) return;

                    const gridItem = document.createElement('div');
                    gridItem.className = 'psn-grid-item';

                    // Trova l'indice dell'ultima notazione per questo turno per evidenziare 'current'
                    const lastNotationIndexForTurn = state.notationsHistory.findLastIndex(n => n.turnNumber == turnNumber);
                     if (lastNotationIndexForTurn === state.notationsHistory.length - 1) {
                         gridItem.classList.add('current');
                     }

                    // Formatta mosse standard
                    let whiteDisplay = highlightNotation(turnData.moves.white ?? ''); // Modificato da '...' a ''
                    let blackDisplay = highlightNotation(turnData.moves.black ?? '');

                    // Prepara HTML per le pescate
                    let whiteDrawHtml = '';
                    if (turnData.draws.white.length > 0) {
                        // Se la mossa bianca era '' (o '...'), non aggiungere spazio extra
                        if (whiteDisplay !== '') whiteDrawHtml += ' '; // Aggiungi spazio separatore se c'era una mossa
                        whiteDrawHtml += turnData.draws.white.map(d => `<span class="card-draw-notation">${highlightNotation(d)}</span>`).join(' ');
                    }
                    let blackDrawHtml = '';
                     if (turnData.draws.black.length > 0) {
                         // Se non c'era mossa nera, non aggiungere spazio extra
                         if (blackDisplay !== '') blackDrawHtml += ' '; // Aggiungi spazio separatore se c'era una mossa
                         blackDrawHtml += turnData.draws.black.map(d => `<span class="card-draw-notation">${highlightNotation(d)}</span>`).join(' ');
                     }

                    // Costruisci l'HTML finale per l'item
                    gridItem.innerHTML = `
                        <span class="psn-turn-number">${turnNumber}.</span>
                        <span class="psn-white-move">${whiteDisplay}${whiteDrawHtml}</span> ; <span class="psn-black-move">${blackDisplay}${blackDrawHtml}</span>
                    `;

                    state.components.gridContainer.appendChild(gridItem);
                });

            // Sempre auto-scroll alla fine
            state.components.gridContainer.scrollTop = state.components.gridContainer.scrollHeight;

        } catch (error) {
            console.error('[PSN] Errore nell\'aggiornamento della visualizzazione a griglia:', error);
        }
    }

    // Formatta la notazione PSN per la visualizzazione
    function formatPSNForDisplay(psnContent) {
        if (!psnContent) return '<div style="padding:20px; text-align:center; color:#80b0e0; font-style:italic;">Nessuna mossa registrata</div>';

        // Utilizzo di un approccio più semplice con classi invece di stili inline
        let formattedHtml = '<table class="psn-table">' +
            '<thead><tr>' +
            '<th class="psn-header">Bianco</th>' +
            '<th class="psn-header">Nero</th>' +
            '</tr></thead><tbody>';

        // Suddividi per linee
        const lines = psnContent.split('\n');

        // Formatta le righe di setup e mosse
        if (lines.length > 0) {
            // Prima riga (0.) è la riga principale di setup
            if (lines[0].startsWith('0.')) {
                // Dividiamo la riga di setup in due parti: prima e dopo il punto e virgola
                const setupParts = lines[0].split(';');
                const whitePart = setupParts[0] || '0.e3|:white';
                const blackPart = setupParts[1] || ':black';

                // Aggiungi la riga di setup principale
                formattedHtml += '<tr class="psn-setup-row">' +
                    '<td class="psn-setup-cell">' + whitePart + '</td>' +
                    '<td class="psn-setup-cell">' + blackPart + '</td>' +
                    '</tr>';
            }

            // Processa tutte le righe di mosse
            for (let i = 1; i < lines.length; i++) {
                const line = lines[i];
                if (!line.trim()) continue;

                // Normalizza la linea rimuovendo spazi eccessivi prima del match
                const normalizedLine = line.replace(/\s+/g, ' ').trim();

                // Estrai turno e mosse con pattern più robusto e semplice
                const turnMatch = normalizedLine.match(/^(\d+)\.\s*(.*?)\s*;\s*(.*)$/);

                // Ogni mossa deve avere la sua riga per garantire che tutte siano visibili
                const rowClass = 'psn-setup-row';
                const cellClass = 'psn-setup-cell';

                if (turnMatch) {
                    const [, turnNumber, whiteMove, blackMove] = turnMatch;

                    // Aggiungi riga alla tabella per mosse standard
                    formattedHtml += '<tr class="' + rowClass + '">' +
                        '<td class="' + cellClass + '">' +
                        '<span class="psn-turn-number">' + turnNumber + '.</span> ' +
                        highlightNotation(whiteMove || '') + // Modificato da '...'
                        '</td>' +
                        '<td class="' + cellClass + '">' +
                        highlightNotation(blackMove || '') +
                        '</td>' +
                        '</tr>';
                } else {
                    // Gestisci diversi formati di notazione per la pesca delle carte

                    // Formato 1: Pesca carta del bianco (es. "12. P9|2")
                    const whiteDrawMatch = normalizedLine.match(/^(\d+)\.\s*([A-Z][0-9]+\|\d+|Draw\|\d+)$/i);
                    if (whiteDrawMatch) {
                        const [, turnNumber, cardDraw] = whiteDrawMatch;

                        // Aggiungi riga alla tabella per pesca carta bianco
                        formattedHtml += '<tr class="' + rowClass + '">' +
                            '<td class="' + cellClass + '">' +
                            '<span class="psn-turn-number">' + turnNumber + '.</span> ' +
                            highlightNotation(cardDraw) +
                            '</td>' +
                            '<td class="' + cellClass + '">' +
                            '' + // Nessuna mossa per il nero
                            '</td>' +
                            '</tr>';
                        continue; // Vai alla prossima iterazione
                    }

                    // Formato 2: Pesca carta del nero (es. "12. ... ; P9|2")
                    const blackDrawMatch = normalizedLine.match(/^(\d+)\.\s*(?:\.\.\.)\s*;\s*([A-Z][0-9]+\|\d+|Draw\|\d+)$/i);
                    if (blackDrawMatch) {
                        const [, turnNumber, cardDraw] = blackDrawMatch;

                        // Aggiungi riga alla tabella per pesca carta nero
                        formattedHtml += '<tr class="' + rowClass + '">' +
                            '<td class="' + cellClass + '">' +
                            '<span class="psn-turn-number">' + turnNumber + '.</span> ' +
                            '' + // Indicatore di mossa saltata per il bianco, ora stringa vuota
                            '</td>' +
                            '<td class="' + cellClass + '">' +
                            highlightNotation(cardDraw) +
                            '</td>' +
                            '</tr>';
                        continue; // Vai alla prossima iterazione
                    }

                    // Formato 3: Caso generico - verifica se la linea contiene notazioni di pesca carta
                    // in formati non standard
                    const genericMatch = normalizedLine.match(/^(\d+)\.\s*(.*?)(?:\s*;\s*(.*))?$/);
                    if (genericMatch) {
                        const [, turnNumber, whitePart, blackPart] = genericMatch;

                        // Verifica se le parti contengono notazioni di pesca (un solo separatore |)
                        const isWhiteDraw = whitePart && whitePart.trim() !== '' && // Modificato da '...'
                                           whitePart.includes('|') &&
                                           whitePart.split('|').length === 2;

                        const isBlackDraw = blackPart && blackPart.trim() !== '' &&
                                           blackPart.includes('|') &&
                                           blackPart.split('|').length === 2;

                        if (isWhiteDraw || isBlackDraw) {
                            // Aggiungi riga alla tabella per pesca carta generica
                            formattedHtml += '<tr class="' + rowClass + '">' +
                                '<td class="' + cellClass + '">' +
                                '<span class="psn-turn-number">' + turnNumber + '.</span> ' +
                                (isWhiteDraw ? highlightNotation(whitePart.trim()) : '') + // Modificato da '...'
                                '</td>' +
                                '<td class="' + cellClass + '">' +
                                (isBlackDraw ? highlightNotation(blackPart.trim()) : '') +
                                '</td>' +
                                '</tr>';
                        }
                    }
                }
            }
        }

        // Chiudi la tabella
        formattedHtml += '</tbody></table>';

        return formattedHtml;
    }

    // Evidenzia parti della notazione con stili inline
    function highlightNotationWithInlineStyles(notation) {
        if (!notation || notation === '...') return notation;

        // Rimuovi eventuali spazi extra aggiunti per l'allineamento prima dell'evidenziazione
        notation = notation.trimEnd();

        // Evidenzia carte (Es. P1, C10, F13) con stili inline
        notation = notation.replace(/([PCF][0-9JQKA]+)/g,
            '<span style="color:#80c0ff; font-weight:bold; background:rgba(60,120,200,0.15); padding:0 2px; border-radius:2px;">$1</span>');

        // Evidenzia posizioni (Es. a1, b2, c3) con stili inline
        notation = notation.replace(/\|([a-f][1-6])\|/g,
            '|<span style="color:#d0f0ff; background:rgba(60,120,220,0.15); padding:0 2px; border-radius:2px;">$1</span>|');

        // Evidenzia carte rimanenti con stili inline
        notation = notation.replace(/\|(\d+)(?!\|)/g,
            '|<span style="color:#90c0f0; background:rgba(40,80,160,0.15); padding:0 2px; border-radius:2px;">$1</span>');

        // Evidenzia controllo vertice con stili inline (colore dorato più visibile)
        notation = notation.replace(/\*/g,
            '<span style="color:#ffdd30; font-weight:bold; text-shadow:0px 0px 2px rgba(255,200,0,0.4);">*</span>');

        // Evidenzia simboli di vittoria con stili inline
        notation = notation.replace(/(!)/g,
            '<span style="color:#ffbb20; font-weight:bold;">$1</span>');
        notation = notation.replace(/(#)/g,
            '<span style="color:#ff8800; font-weight:bold;">$1</span>');

        return notation;
    }

    // Evidenzia parti della notazione
    function highlightNotation(notation) {
        if (!notation || notation === '...') return notation;

        // Rimuovi eventuali spazi extra aggiunti per l'allineamento prima dell'evidenziazione
        notation = notation.trimEnd();

        // Evidenzia carte (Es. P1, C10, F13)
        notation = notation.replace(/([PCF][0-9JQKA]+)/g,
            '<span class="card-notation">$1</span>');

        // Evidenzia posizioni (Es. a1, b2, c3)
        notation = notation.replace(/\|([a-f][1-6])\|/g,
            '|<span class="position-notation">$1</span>|');

        // Evidenzia carte rimanenti (sia per mosse standard che per pesca carta)
        // Modifica per supportare l'asterisco che può seguire il numero di carte rimanenti
        notation = notation.replace(/\|(\d+)(\*)?(?!\|)/g,
            function(match, p1, p2) {
                // p1 = numero di carte, p2 = eventuale asterisco
                if (p2) {
                    return '|<span class="remaining-cards">' + p1 + '</span><span class="control-marker">*</span>';
                } else {
                    return '|<span class="remaining-cards">' + p1 + '</span>';
                }
            });

        // Gestione speciale per notazioni di pesca carta (es. P9|2 o Draw|2)
        // Cerca un pattern di carta o "Draw" seguito da | e un numero alla fine della stringa
        if (/([PCF][0-9JQKA]+|Draw)\|\d+$/i.test(notation)) {
            // Questo è già gestito dagli stili sopra, qui si può aggiungere una classe speciale
            notation = notation.replace(/([PCF][0-9JQKA]+|Draw)\|\d+$/i,
                '<span class="card-draw-notation">$&</span>');

            // Aggiungi solo un'icona per pesca carta, senza testo "Draw:"
            notation = notation.replace(/(Draw)/i,
                '<i class="fas fa-hand-paper" style="margin-right: 3px;"></i>');
        }

        // Evidenzia controllo vertice (per eventuali asterischi non catturati dalla regex precedente)
        notation = notation.replace(/\*/g,
            '<span class="control-marker">*</span>');

        // Evidenzia simboli di vittoria
        notation = notation.replace(/(!)/g,
            '<span class="win-marker">$1</span>');
        notation = notation.replace(/(#)/g,
            '<span class="checkmate-marker">$1</span>');

        return notation;
    }

    // Formatta le carte in mano
    function formatHand(cards) {
        if (!cards || cards.length === 0) {
            return '';
        }

        return cards.join('/');
    }

    // Formatta le mani per la visualizzazione nella griglia
    function formatHandForDisplay(cards, playerName) {
        // Creazione dell'intestazione
        let html = `<div class="hand-header">Mano del ${playerName}</div>`;

        // Se non ci sono carte, mostra un messaggio
        if (!cards || cards.length === 0) {
            html += '<div class="empty-hand">Nessuna carta</div>';
            return html;
        }

        // Contenitore per le carte
        html += '<div class="hand-cards-container">';

        // Aggiungi ogni carta
        cards.forEach(card => {
            const highlightedCard = highlightNotation(card);
            html += `<div class="hand-card">${highlightedCard}</div>`;
        });

        html += '</div>';

        return html;
    }

    // Ottieni la notazione di una carta
    function getCardNotation(card) {
        if (!card || !card.suit || !card.value) {
            return '??';
        }

        // Converti il nome del seme nella notazione corretta
        let suit = '';
        switch (card.suit.toLowerCase()) {
            case 'rock':
                suit = 'P'; // Pietra
                break;
            case 'paper':
                suit = 'C'; // Carta
                break;
            case 'scissors':
                suit = 'F'; // Forbice
                break;
            default:
                suit = card.suit.charAt(0).toUpperCase();
        }

        // Converti i valori delle carte face ai valori numerici per la notazione PSN
        let value = '';
        switch (card.value) {
            case 'A':
                value = '1';
                break;
            case 'J':
                value = '11';
                break;
            case 'Q':
                value = '12';
                break;
            case 'K':
                value = '13';
                break;
            default:
                value = card.value;
        }

        return `${suit}${value}`;
    }

    // Controllo periodico dello stato PSN
    function periodicCheck() {
        // Verifica se siamo in una partita attiva
        const isActiveGame = document.querySelector('#game-container:not([style*="display: none"])');

        if (!isActiveGame) {
            return;
        }

        // Verifica se l'area PSN esiste
        if (!document.getElementById('skemino-notation-area')) {
            console.log('[PSN] Controllo periodico: Area PSN non trovata, ricreazione...');
            ensurePSNArea();
        }

        // Verifica se il contenitore del contenuto PSN esiste
        if (!document.getElementById('psn-content')) {
            console.log('[PSN] Controllo periodico: Contenitore PSN mancante, correzione...');
            ensurePSNArea();
        }

        // Verifica lo stato del sistema PSN standard
        checkStandardPSNComponents();

        // Durante l'animazione iniziale, non mostriamo la notazione automaticamente
        // Verifichiamo solo se c'è già contenuto nel PSN
        const hasContent = state.psnContent && state.psnContent.trim().length > 0 &&
                          state.psnContent.trim() !== '0.e3||:white;:black';
        const contentEl = document.getElementById('psn-content');

        // Ripristiniamo solo se c'è contenuto effettivo (non solo l'intestazione) ma è scomparso dall'interfaccia
        if (hasContent && contentEl && (!contentEl.textContent || contentEl.textContent.trim() === '')) {
            console.log('[PSN] Controllo periodico: Ripristino notazione esistente');
            updateNotationDisplay(state.psnContent);
        }

        // ✅ NUOVO: Controllo speciale per sincronizzazione dopo visibilità browser
        // Se il browser è appena tornato visibile e abbiamo mosse registrate ma la vista non è sincronizzata
        if (document.visibilityState === 'visible' && state.moveHistory.length > 0) {
            // Controlla se dovremmo essere in vista Notazioni ma siamo ancora in vista Mani
            if (state.moveHistory.length >= 1 && state.showHandsOnly && !state.firstMoveMadeSwitchDone) {
                console.log('[PSN] Controllo periodico: Prima mossa presente ma vista non sincronizzata - correzione...');
                state.showHandsOnly = false;
                state.firstMoveMadeSwitchDone = true;

                // Aggiorna il testo del pulsante toggle
                const gridToggleBtn = document.querySelector('.psn-grid-toggle-btn');
                if (gridToggleBtn) {
                    gridToggleBtn.innerHTML = '<i class="fas fa-hand-paper"></i> Mostra Mani';
                }

                // Forza aggiornamento della visualizzazione
                updateGridDisplay();
                console.log('[PSN] Vista sincronizzata a Notazioni dopo controllo periodico');
            }

            // Forza sincronizzazione con il sistema PSN standard se disponibile
            if (checkStandardPSNComponents()) {
                let standardContent = '';

                if (window.psnMoves && typeof window.psnMoves.getPSNContent === 'function') {
                    standardContent = window.psnMoves.getPSNContent();
                } else if (window.enhancedPSN && typeof window.enhancedPSN.getPSNContent === 'function') {
                    standardContent = window.enhancedPSN.getPSNContent();
                }

                if (standardContent && standardContent.trim() &&
                    standardContent !== '0.e3||:white;:black' &&
                    standardContent !== state.psnContent) {
                    console.log('[PSN] Controllo periodico: Sincronizzazione contenuto PSN standard');
                    state.psnContent = standardContent;
                    updateNotationDisplay(standardContent);
                }
            }
        }
    }
})();