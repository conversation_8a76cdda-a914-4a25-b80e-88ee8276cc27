/**
 * Online Play Enhancer - Garantisce l'applicazione della classe online-play-interface
 * per migliorare l'interfaccia di gioco online
 * 
 * DETTAGLI DEL FUNZIONAMENTO:
 * 
 * 1. SCOPO:
 *    - Applica stili speciali per l'interfaccia del gioco online
 *    - Modifica il layout per mostrare correttamente i timer e la chat
 *    - Attiva la classe 'online-play-interface' sul game-container
 *    - Imposta larghezza fissa per la colonna dei giocatori
 * 
 * 2. PULSANTI CHE ATTIVANO GLI STILI:
 *    - quick-game-button: da index.html (partita rapida)
 *    - new-game-button: da home-logged.html (nuova partita)
 *    - Gli stili NON si attivano per play-online-button (partita 30 minuti)
 * 
 * 3. FLUSSO DI ESECUZIONE:
 *    - Al click sui pulsanti appropriati, imposta quickGameButtonClicked = true
 *    - Per home-logged, salva anche flag in sessionStorage per il redirect
 *    - Al caricamento della pagina, controlla sessionStorage per il flag
 *    - Osserva le modifiche al game-container e applica stili quando diventa visibile
 * 
 * 4. STILI APPLICATI:
 *    - Aggiunge classe 'online-play-interface' al game-container
 *    - Imposta larghezza fissa di 960px per players-column
 *    - Forza flex: 0 0 960px per evitare ridimensionamenti
 * 
 * 5. TIMING:
 *    - Applica stili con delay per permettere il caricamento completo
 *    - Usa MutationObserver per rilevare quando game-container diventa visibile
 *    - Ascolta eventi custom 'gameContainerShown'
 * 
 * 6. PERSISTENZA:
 *    - Usa sessionStorage per mantenere il flag attraverso i redirect
 *    - Rimuove il flag dopo l'uso per evitare applicazioni non volute
 */

(function() {
    // Variabile globale per tracciare se il pulsante "Gioca Online" è stato cliccato
    let quickGameButtonClicked = false;
    
    // Flag per evitare applicazioni multiple degli stili
    let interfaceApplied = false;
    
    // THROTTLING: Variabili per limitare i log eccessivi
    let lastStyleModificationLogTime = 0;
    
    // Funzione principale per applicare la classe - solo quando appropriato
    function applyOnlinePlayInterface() {
        console.log('[ONLINE ENHANCER] Applicazione classe online-play-interface...');
        
        // Verifica se siamo nella schermata di gioco E se è stato cliccato il pulsante giusto
        // E se non abbiamo già applicato l'interfaccia
        const gameContainer = document.getElementById('game-container');
        if (gameContainer && quickGameButtonClicked && !interfaceApplied) {
            gameContainer.classList.add('online-play-interface');
            interfaceApplied = true; // Segna che abbiamo già applicato l'interfaccia
            console.log('[ONLINE ENHANCER] Classe online-play-interface applicata al game-container');
            
            // Applica la classe anche alla players-column per i selettori CSS
            const playersColumn = document.getElementById('players-column');
            if (playersColumn) {
                playersColumn.classList.add('online-play-interface');
                console.log('[ONLINE ENHANCER] Classe applicata alla players-column per selettori CSS');
                
                // Log di debug per verificare che gli stili siano stati applicati
                setTimeout(() => {
                    const computedStyle = window.getComputedStyle(playersColumn);
                    console.log('[ONLINE ENHANCER DEBUG] Players-column computed width:', computedStyle.width);
                    console.log('[ONLINE ENHANCER DEBUG] Players-column computed flex:', computedStyle.flex);
                    console.log('[ONLINE ENHANCER DEBUG] Game-container computed grid-template-columns:', window.getComputedStyle(gameContainer).gridTemplateColumns);
                }, 100);
            }
            
            console.log('[ONLINE ENHANCER] Utilizzo esclusivamente stili da CSS');
        } else if (interfaceApplied) {
            console.log('[ONLINE ENHANCER] Interfaccia già applicata, evito ridondanza');
        } else {
            console.log('[ONLINE ENHANCER] Non applico la classe (pulsante non cliccato o non nella schermata di gioco)');
        }
    }

    // Ascoltatore per il caricamento della pagina
    document.addEventListener('DOMContentLoaded', function() {
        console.log('[ONLINE ENHANCER] DOM pronto, inizializzo enhancer...');
        
        // Controlla se abbiamo un flag salvato in sessionStorage
        const savedOnlinePlayFlag = sessionStorage.getItem('onlinePlayInterface');
        if (savedOnlinePlayFlag === 'true') {
            console.log('[ONLINE ENHANCER] Flag online play trovato in sessionStorage');
            quickGameButtonClicked = true;
            sessionStorage.removeItem('onlinePlayInterface');
            
            // CSS ora spostato nel file online-interface.css
            console.log('[ONLINE ENHANCER] CSS di emergenza già presente nel file CSS');
            
            // Applica IMMEDIATAMENTE gli stili senza timeout
            applyOnlinePlayInterface();
        }
        
        // Osserva le modifiche alla visibilità del game-container
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'attributes' &&
                    mutation.attributeName === 'style' &&
                    mutation.target.id === 'game-container') {

                    console.log('[ONLINE ENHANCER] Rilevata modifica allo style del game-container');
                    // Applica solo se il pulsante giusto è stato cliccato e non è già stata applicata l'interfaccia
                    if (quickGameButtonClicked && !interfaceApplied) {
                        // Applica immediatamente senza timeout
                        applyOnlinePlayInterface();
                    }
                }
            });
        });

        // Inizia l'osservazione
        const gameContainer = document.getElementById('game-container');
        if (gameContainer) {
            observer.observe(gameContainer, { attributes: true });
        }
        
        // Imposta il flag SOLO quando viene fatto clic sul pulsante "Gioca Online"
        const playOnlineButton = document.getElementById('play-online-button');
        const quickGameButton = document.getElementById('quick-game-button');
        const newGameButton = document.getElementById('new-game-button'); // Per home-logged.html
        
        // NON attivare gli stili sul pulsante "Gioca a 30 minuti"
        if (playOnlineButton) {
            playOnlineButton.addEventListener('click', function() {
                console.log('[ONLINE ENHANCER] Clic su play-online-button - NON applico stili');
                // Non fare nulla, questo pulsante non deve avere gli stili speciali
                quickGameButtonClicked = false;
            });
        }
        
        // SOLO questo pulsante attiva gli stili speciali
        if (quickGameButton) {
            quickGameButton.addEventListener('click', function() {
                console.log('[ONLINE ENHANCER] Clic su quick-game-button - ATTIVO stili');
                quickGameButtonClicked = true;
                // Reset dell'interfaccia all'inizio di una nuova partita
                interfaceApplied = false;
                // Applica immediatamente
                applyOnlinePlayInterface();
            });
        }
        
        // Attiva anche per il pulsante "Nuova Partita" da home-logged
        if (newGameButton) {
            newGameButton.addEventListener('click', function() {
                console.log('[ONLINE ENHANCER] Clic su new-game-button - ATTIVO stili');
                quickGameButtonClicked = true;
                // Reset dell'interfaccia all'inizio di una nuova partita
                interfaceApplied = false;
                // Salva il flag per quando la pagina viene ricaricata dopo il matchmaking
                sessionStorage.setItem('onlinePlayInterface', 'true');
                // Non usiamo più questo timeout poiché verrà gestito dopo il redirect
            });
        }
        
        // Ascolta anche gli eventi personalizzati, ma applica solo se è stato premuto il pulsante giusto
        document.addEventListener('gameContainerShown', function() {
            console.log('[ONLINE ENHANCER] Evento gameContainerShown ricevuto');
            if (quickGameButtonClicked && !interfaceApplied) {
                applyOnlinePlayInterface();
                console.log('[ONLINE ENHANCER] Pulsante Gioca Online cliccato, applico stili');
                
                // Trigger smooth loading animations
                if (window.smoothLoading) {
                    setTimeout(() => {
                        window.smoothLoading.trigger();
                    }, 100);
                }
            } else if (interfaceApplied) {
                console.log('[ONLINE ENHANCER] Interfaccia già applicata, evito ridondanza');
                
                // Trigger smooth loading animations anyway for consistency
                if (window.smoothLoading && !window.smoothLoading.isComplete()) {
                    setTimeout(() => {
                        window.smoothLoading.trigger();
                    }, 50);
                }
            } else {
                console.log('[ONLINE ENHANCER] Pulsante Gioca Online non cliccato, non applico stili');
            }
        });
    });
    
    // Ascolta anche quando la finestra è caricata completamente (incluse immagini)
    window.addEventListener('load', function() {
        console.log('[ONLINE ENHANCER] Finestra completamente caricata');
        // Applica solo se il pulsante giusto è stato cliccato e l'interfaccia non è già stata applicata
        if (quickGameButtonClicked && !interfaceApplied) {
            applyOnlinePlayInterface();
            console.log('[ONLINE ENHANCER] Pulsante Gioca Online cliccato, applico stili al caricamento');
        } else if (interfaceApplied) {
            console.log('[ONLINE ENHANCER] Interfaccia già applicata, evito ridondanza al caricamento');
        } else {
            console.log('[ONLINE ENHANCER] Pulsante Gioca Online non cliccato, non applico stili al caricamento');
        }
    });
    
    // Funzione per applicare gli stili immediatamente (per uso nel matchmaking)
    window.applyOnlinePlayInterfaceImmediate = function() {
        console.log('[ONLINE ENHANCER] Applicazione IMMEDIATA della classe online-play-interface');
        quickGameButtonClicked = true;
        interfaceApplied = false; // Reset per permettere l'applicazione
        
        // CSS ora spostato nel file online-interface.css
        console.log('[ONLINE ENHANCER] IMMEDIATO: CSS di emergenza già presente nel file CSS');
        
        // Applica immediatamente senza timeout
        const gameContainer = document.getElementById('game-container');
        if (gameContainer) {
            gameContainer.classList.add('online-play-interface');
            interfaceApplied = true;
            
            // Applica la classe anche alla players-column per i selettori CSS
            const playersColumn = document.getElementById('players-column');
            if (playersColumn) {
                playersColumn.classList.add('online-play-interface');
                console.log('[ONLINE ENHANCER] IMMEDIATO: Classe applicata alla players-column');
            }
            
            console.log('[ONLINE ENHANCER] IMMEDIATO: Classe applicata, stili gestiti dai CSS');
        }
    };
})();